# 招商银行理财产品爬虫 Makefile

.PHONY: help install setup run test clean analyze web lint format

# 默认目标
help:
	@echo "招商银行理财产品爬虫 - 可用命令:"
	@echo ""
	@echo "  install    - 安装项目依赖"
	@echo "  setup      - 初始化项目环境"
	@echo "  run        - 运行爬虫"
	@echo "  test       - 运行测试"
	@echo "  analyze    - 分析爬取数据"
	@echo "  web        - 启动Web查看器"
	@echo "  lint       - 代码检查"
	@echo "  format     - 代码格式化"
	@echo "  clean      - 清理临时文件"
	@echo ""

# 安装依赖
install:
	@echo "🔄 安装项目依赖..."
	python install.py

# 初始化环境
setup:
	@echo "🔄 初始化项目环境..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "✅ 已创建.env文件"; fi
	@echo "⚠️  请编辑.env文件配置API密钥"

# 运行爬虫
run:
	@echo "🚀 启动爬虫..."
	python run.py

# 运行测试
test:
	@echo "🧪 运行测试..."
	python -m pytest tests/ -v

# 分析数据
analyze:
	@echo "📊 分析爬取数据..."
	python analyze_data.py

# 启动Web查看器
web:
	@echo "🌐 启动Web查看器..."
	python web_viewer.py

# 代码检查
lint:
	@echo "🔍 代码检查..."
	@if command -v flake8 >/dev/null 2>&1; then \
		flake8 src/ tests/ --max-line-length=88 --ignore=E203,W503; \
	else \
		echo "⚠️  flake8未安装，跳过代码检查"; \
	fi

# 代码格式化
format:
	@echo "✨ 代码格式化..."
	@if command -v black >/dev/null 2>&1; then \
		black src/ tests/ --line-length=88; \
	else \
		echo "⚠️  black未安装，跳过代码格式化"; \
	fi
	@if command -v isort >/dev/null 2>&1; then \
		isort src/ tests/ --profile black; \
	else \
		echo "⚠️  isort未安装，跳过导入排序"; \
	fi

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name ".coverage" -delete 2>/dev/null || true
	@echo "✅ 清理完成"

# 开发环境设置
dev-install:
	@echo "🔧 安装开发依赖..."
	uv pip install -e ".[dev]"

# 构建分发包
build:
	@echo "📦 构建分发包..."
	python -m build

# 查看项目状态
status:
	@echo "📋 项目状态:"
	@echo "Python版本: $(shell python --version)"
	@echo "项目目录: $(shell pwd)"
	@echo "虚拟环境: $(shell which python)"
	@if [ -f .env ]; then echo "✅ .env文件存在"; else echo "❌ .env文件不存在"; fi
	@if [ -d data ]; then echo "✅ data目录存在"; else echo "❌ data目录不存在"; fi
	@if [ -d logs ]; then echo "✅ logs目录存在"; else echo "❌ logs目录不存在"; fi

# 快速开始
quickstart: install setup
	@echo ""
	@echo "🎉 快速开始完成!"
	@echo "现在可以运行: make run"
