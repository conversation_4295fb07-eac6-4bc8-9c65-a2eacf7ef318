[project]
name = "apas-crawl"
version = "0.1.0"
description = "Bank financial product crawler using crawl4ai"
authors = [
    {name = "i<PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crawl4ai>=0.3.0",
    "aiohttp>=3.9.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "selenium>=4.15.0",
    "requests>=2.31.0",
    "fake-useragent>=1.4.0",
    "asyncio>=3.4.3",
    "aiofiles>=23.2.0",
    "pydantic>=2.5.0",
    "loguru>=0.7.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "playwright>=1.40.0",
    "httpx>=0.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
