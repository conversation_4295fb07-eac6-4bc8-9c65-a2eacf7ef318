#!/usr/bin/env python3
"""
爬虫运行脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import run_crawler

if __name__ == "__main__":
    print("招商银行理财产品爬虫")
    print("=" * 40)
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("警告: 未找到.env文件，请复制.env.example并配置相关参数")
        print("继续使用默认配置...")
    
    # 运行爬虫
    try:
        result = run_crawler()
        if result:
            print(f"\n✅ 爬取成功完成!")
            print(f"📊 总计: {result.total_products} 个产品")
            print(f"✅ 成功: {result.successful_products} 个")
            print(f"❌ 失败: {result.failed_products} 个")
            print(f"📁 数据保存在: data/ 目录")
        else:
            print("\n❌ 爬取失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n💥 运行异常: {e}")
        sys.exit(1)
