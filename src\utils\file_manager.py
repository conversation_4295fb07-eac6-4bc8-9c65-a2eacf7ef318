"""
文件管理工具
"""
import json
import aiofiles
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional
from bs4 import BeautifulSoup
from loguru import logger
from src.config.settings import config
from src.models.product import FinancialProduct, CrawlResult


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.data_dir = config.DATA_DIR
        self.html_dir = self.data_dir / "html"
        self.json_dir = self.data_dir / "json"
        self.network_dir = self.data_dir / "network"
        
        # 确保目录存在
        for dir_path in [self.html_dir, self.json_dir, self.network_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    async def save_html(self, content: str, filename: str, 
                       subdirectory: Optional[str] = None) -> Path:
        """保存HTML文件"""
        if subdirectory:
            save_dir = self.html_dir / subdirectory
            save_dir.mkdir(parents=True, exist_ok=True)
        else:
            save_dir = self.html_dir
        
        file_path = save_dir / f"{filename}.html"
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)
        
        logger.info(f"HTML文件已保存: {file_path}")
        return file_path
    
    async def save_cleaned_html(self, content: str, filename: str,
                              subdirectory: Optional[str] = None) -> Path:
        """保存清洗后的HTML文件"""
        # 使用BeautifulSoup清洗HTML
        soup = BeautifulSoup(content, 'html.parser')
        
        # 移除脚本和样式标签
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 移除注释
        from bs4 import Comment
        comments = soup.findAll(text=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
        
        # 获取清洗后的HTML
        cleaned_html = soup.prettify()
        
        if subdirectory:
            save_dir = self.html_dir / subdirectory
            save_dir.mkdir(parents=True, exist_ok=True)
        else:
            save_dir = self.html_dir
        
        file_path = save_dir / f"{filename}_cleaned.html"
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(cleaned_html)
        
        logger.info(f"清洗后的HTML文件已保存: {file_path}")
        return file_path
    
    async def save_json(self, data: Any, filename: str,
                       subdirectory: Optional[str] = None) -> Path:
        """保存JSON文件"""
        if subdirectory:
            save_dir = self.json_dir / subdirectory
            save_dir.mkdir(parents=True, exist_ok=True)
        else:
            save_dir = self.json_dir
        
        file_path = save_dir / f"{filename}.json"
        
        # 处理Pydantic模型
        if hasattr(data, 'dict'):
            json_data = data.dict()
        elif hasattr(data, 'model_dump'):
            json_data = data.model_dump()
        else:
            json_data = data
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(json_data, ensure_ascii=False, indent=2))
        
        logger.info(f"JSON文件已保存: {file_path}")
        return file_path
    
    async def save_network_request(self, url: str, response_data: Any,
                                 filename: str) -> Path:
        """保存网络请求数据"""
        file_path = self.network_dir / f"{filename}.json"
        
        data = {
            "url": url,
            "response_data": response_data,
            "timestamp": datetime.now().isoformat()
        }
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data, ensure_ascii=False, indent=2))
        
        logger.info(f"网络请求数据已保存: {file_path}")
        return file_path
    
    async def save_product(self, product: FinancialProduct) -> Dict[str, Path]:
        """保存单个产品的所有数据"""
        product_id = product.product_id
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{product_id}_{timestamp}"
        
        saved_files = {}
        
        # 保存产品JSON数据
        json_path = await self.save_json(product, filename, "products")
        saved_files["json"] = json_path
        
        # 如果有网络请求数据，保存到单独的文件
        if product.network_requests:
            for i, request in enumerate(product.network_requests):
                request_filename = f"{filename}_request_{i}"
                request_path = await self.save_network_request(
                    request.url, 
                    request.response_data,
                    request_filename
                )
                saved_files[f"network_{i}"] = request_path
        
        return saved_files
    
    async def save_crawl_result(self, result: CrawlResult) -> Path:
        """保存爬取结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"crawl_result_{timestamp}"
        
        return await self.save_json(result, filename)
    
    async def load_json(self, file_path: Path) -> Any:
        """加载JSON文件"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    
    def get_latest_file(self, directory: Path, pattern: str = "*") -> Optional[Path]:
        """获取目录中最新的文件"""
        files = list(directory.glob(pattern))
        if not files:
            return None
        
        return max(files, key=lambda f: f.stat().st_mtime)
    
    def cleanup_old_files(self, directory: Path, keep_count: int = 10):
        """清理旧文件，保留最新的几个"""
        files = sorted(directory.glob("*"), key=lambda f: f.stat().st_mtime, reverse=True)
        
        for file_path in files[keep_count:]:
            try:
                file_path.unlink()
                logger.info(f"已删除旧文件: {file_path}")
            except Exception as e:
                logger.error(f"删除文件失败 {file_path}: {e}")
    
    def generate_filename(self, prefix: str, product_id: str = None) -> str:
        """生成文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if product_id:
            return f"{prefix}_{product_id}_{timestamp}"
        return f"{prefix}_{timestamp}"
