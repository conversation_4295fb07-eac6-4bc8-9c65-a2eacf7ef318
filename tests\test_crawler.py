"""
爬虫测试
"""
import pytest
import asyncio
from src.crawler.cmb_crawler import CMBCrawler
from src.utils.data_extractor import DataExtractor
from src.utils.file_manager import FileManager
from src.config.settings import config


@pytest.mark.asyncio
async def test_crawler_initialization():
    """测试爬虫初始化"""
    crawler = CMBCrawler()
    assert crawler.base_url == "https://mobile.cmbchina.com"
    assert crawler.target_url == config.TARGET_URL


@pytest.mark.asyncio
async def test_data_extractor():
    """测试数据提取器"""
    extractor = DataExtractor()
    
    # 测试HTML文本提取
    html = "<html><body><h1>测试产品</h1><p>收益率: 4.5%</p></body></html>"
    text = extractor.extract_text_from_html(html)
    assert "测试产品" in text
    assert "4.5%" in text
    
    # 测试基础信息提取
    info = extractor.extract_basic_info_from_html(html)
    assert info.get('name') == "测试产品"


def test_file_manager():
    """测试文件管理器"""
    file_manager = FileManager()
    assert file_manager.data_dir.exists()
    assert file_manager.html_dir.exists()
    assert file_manager.json_dir.exists()


@pytest.mark.asyncio
async def test_product_list_parsing():
    """测试产品列表解析"""
    crawler = CMBCrawler()
    
    # 模拟HTML内容
    html_content = """
    <div class="product-list">
        <div class="product-item">
            <h3>招银理财产品A</h3>
            <span>预期收益率: 4.2%</span>
            <span>风险等级: PR2</span>
            <span>起购金额: 1万元</span>
        </div>
        <div class="product-item">
            <h3>招银理财产品B</h3>
            <span>预期收益率: 3.8%</span>
            <span>风险等级: PR1</span>
            <span>起购金额: 5万元</span>
        </div>
    </div>
    """
    
    products = await crawler._parse_product_list_detailed(html_content)
    assert len(products) >= 2
    
    # 检查第一个产品
    product1 = products[0]
    assert "招银理财产品A" in product1.get('name', '')
    assert product1.get('expected_return') == '4.2%'
    assert product1.get('risk_level') == 'PR2'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
