"""
主程序入口
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger
from src.config.settings import config
from src.crawler.cmb_crawler import CMBCrawler
from src.utils.file_manager import FileManager


def setup_logging():
    """设置日志"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台日志
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 添加文件日志
    log_file = config.LOGS_DIR / f"crawler_{datetime.now().strftime('%Y%m%d')}.log"
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="1 day",
        retention="7 days",
        compression="zip"
    )


async def main():
    """主函数"""
    setup_logging()
    logger.info("=" * 60)
    logger.info("招商银行理财产品爬虫启动")
    logger.info("=" * 60)
    
    file_manager = FileManager()
    
    try:
        # 检查配置
        if not config.DEEPSEEK_API_KEY:
            logger.warning("未配置DeepSeek API密钥，将跳过AI数据提取功能")
        
        # 创建爬虫实例
        async with CMBCrawler() as crawler:
            logger.info(f"目标URL: {config.TARGET_URL}")
            logger.info(f"最大产品数量: {config.MAX_PRODUCTS}")
            logger.info(f"并发限制: {config.CONCURRENT_LIMIT}")
            
            # 开始爬取
            start_time = datetime.now()
            logger.info(f"开始时间: {start_time}")
            
            # 爬取所有产品
            result = await crawler.crawl_all_products()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            # 输出结果统计
            logger.info("=" * 60)
            logger.info("爬取完成统计")
            logger.info("=" * 60)
            logger.info(f"总产品数量: {result.total_products}")
            logger.info(f"成功爬取: {result.successful_products}")
            logger.info(f"失败数量: {result.failed_products}")
            logger.info(f"成功率: {result.successful_products/result.total_products*100:.1f}%" if result.total_products > 0 else "0%")
            logger.info(f"耗时: {duration}")
            
            # 保存爬取结果
            result.crawl_end_time = end_time
            result_file = await file_manager.save_crawl_result(result)
            logger.info(f"爬取结果已保存: {result_file}")
            
            # 输出错误信息
            if result.errors:
                logger.warning("爬取过程中的错误:")
                for error in result.errors:
                    logger.warning(f"  - {error}")
            
            # 输出网络请求统计
            network_stats = crawler.network_monitor.get_statistics()
            if network_stats['total_requests'] > 0:
                logger.info("=" * 60)
                logger.info("网络请求统计")
                logger.info("=" * 60)
                logger.info(f"总请求数: {network_stats['total_requests']}")
                
                if network_stats['by_keyword']:
                    logger.info("按关键字统计:")
                    for keyword, count in network_stats['by_keyword'].items():
                        logger.info(f"  {keyword}: {count}")
                
                if network_stats['by_status']:
                    logger.info("按状态码统计:")
                    for status, count in network_stats['by_status'].items():
                        logger.info(f"  {status}: {count}")
            
            # 输出保存的文件信息
            logger.info("=" * 60)
            logger.info("数据文件保存位置")
            logger.info("=" * 60)
            logger.info(f"HTML文件: {config.DATA_DIR / 'html'}")
            logger.info(f"JSON文件: {config.DATA_DIR / 'json'}")
            logger.info(f"网络请求: {config.DATA_DIR / 'network'}")
            logger.info(f"日志文件: {config.LOGS_DIR}")
            
            # 清理旧文件
            logger.info("清理旧文件...")
            file_manager.cleanup_old_files(config.DATA_DIR / "html", keep_count=20)
            file_manager.cleanup_old_files(config.DATA_DIR / "json", keep_count=20)
            file_manager.cleanup_old_files(config.DATA_DIR / "network", keep_count=50)
            
            logger.info("=" * 60)
            logger.info("爬虫任务完成")
            logger.info("=" * 60)
            
            return result
            
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        return None
    except Exception as e:
        logger.error(f"爬取过程发生异常: {e}")
        raise


def run_crawler():
    """运行爬虫的同步入口"""
    try:
        # 在Windows上设置事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行异步主函数
        result = asyncio.run(main())
        return result
    except Exception as e:
        logger.error(f"运行爬虫失败: {e}")
        return None


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("错误: 需要Python 3.9或更高版本")
        sys.exit(1)
    
    # 运行爬虫
    result = run_crawler()
    
    if result:
        print(f"\n爬取完成! 成功获取 {result.successful_products} 个产品")
        print(f"数据保存在: {config.DATA_DIR}")
    else:
        print("\n爬取失败或被中断")
        sys.exit(1)
