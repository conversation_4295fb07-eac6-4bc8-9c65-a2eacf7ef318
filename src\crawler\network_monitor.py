"""
网络请求监听器
"""
import json
import asyncio
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime
from loguru import logger
from src.config.settings import config
from src.models.product import NetworkRequest


class NetworkMonitor:
    """网络请求监听器"""
    
    def __init__(self):
        self.captured_requests: List[NetworkRequest] = []
        self.keywords = config.NETWORK_KEYWORDS
        self.is_monitoring = False
        
    def should_capture_request(self, url: str) -> bool:
        """判断是否应该捕获该请求"""
        return any(keyword in url for keyword in self.keywords)
    
    async def setup_page_monitoring(self, page):
        """设置页面请求监听"""
        self.is_monitoring = True
        
        async def handle_request(request):
            """处理请求"""
            try:
                if self.should_capture_request(request.url):
                    logger.info(f"捕获到目标请求: {request.url}")
                    
                    # 等待响应
                    try:
                        response = await request.response()
                        if response:
                            # 尝试获取响应数据
                            response_data = await self._get_response_data(response)
                            
                            # 创建网络请求记录
                            network_request = NetworkRequest(
                                url=request.url,
                                method=request.method,
                                headers=dict(request.headers),
                                response_data=response_data,
                                status_code=response.status,
                                timestamp=datetime.now()
                            )
                            
                            self.captured_requests.append(network_request)
                            logger.info(f"已记录网络请求: {request.url}")
                    except Exception as e:
                        logger.error(f"获取响应数据失败 {request.url}: {e}")
                        
            except Exception as e:
                logger.error(f"处理请求失败 {request.url}: {e}")
        
        # 监听请求
        page.on("request", handle_request)
        
        # 也可以监听响应
        async def handle_response(response):
            """处理响应"""
            try:
                if self.should_capture_request(response.url):
                    logger.debug(f"响应状态 {response.url}: {response.status}")
            except Exception as e:
                logger.error(f"处理响应失败: {e}")
        
        page.on("response", handle_response)
    
    async def _get_response_data(self, response) -> Optional[Any]:
        """获取响应数据"""
        try:
            content_type = response.headers.get('content-type', '').lower()
            
            if 'application/json' in content_type:
                # JSON响应
                text = await response.text()
                return json.loads(text)
            elif 'text/' in content_type:
                # 文本响应
                return await response.text()
            else:
                # 其他类型，返回基本信息
                return {
                    "content_type": content_type,
                    "size": len(await response.body()) if hasattr(response, 'body') else 0
                }
        except Exception as e:
            logger.error(f"解析响应数据失败: {e}")
            return None
    
    def get_captured_requests(self) -> List[NetworkRequest]:
        """获取捕获的请求"""
        return self.captured_requests.copy()
    
    def clear_captured_requests(self):
        """清空捕获的请求"""
        self.captured_requests.clear()
    
    def get_requests_by_keyword(self, keyword: str) -> List[NetworkRequest]:
        """根据关键字获取请求"""
        return [req for req in self.captured_requests if keyword in req.url]
    
    def stop_monitoring(self):
        """停止监听"""
        self.is_monitoring = False
    
    async def wait_for_requests(self, timeout: int = 10, 
                              min_requests: int = 1) -> List[NetworkRequest]:
        """等待特定数量的请求"""
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            if len(self.captured_requests) >= min_requests:
                break
            await asyncio.sleep(0.5)
        
        return self.get_captured_requests()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取监听统计信息"""
        stats = {
            "total_requests": len(self.captured_requests),
            "by_keyword": {},
            "by_status": {},
            "by_method": {}
        }
        
        for request in self.captured_requests:
            # 按关键字统计
            for keyword in self.keywords:
                if keyword in request.url:
                    stats["by_keyword"][keyword] = stats["by_keyword"].get(keyword, 0) + 1
            
            # 按状态码统计
            status = str(request.status_code) if request.status_code else "unknown"
            stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
            
            # 按方法统计
            method = request.method
            stats["by_method"][method] = stats["by_method"].get(method, 0) + 1
        
        return stats
