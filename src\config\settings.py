"""
配置文件
"""
import os
from pathlib import Path
from typing import List, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 基础配置
class Config:
    # API配置
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL: str = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")
    
    # 爬虫配置
    MAX_PRODUCTS: int = int(os.getenv("MAX_PRODUCTS", "200"))
    CONCURRENT_LIMIT: int = int(os.getenv("CONCURRENT_LIMIT", "5"))
    REQUEST_DELAY: float = float(os.getenv("REQUEST_DELAY", "2.0"))
    RETRY_TIMES: int = int(os.getenv("RETRY_TIMES", "3"))
    TIMEOUT: int = int(os.getenv("TIMEOUT", "30"))
    
    # 目录配置
    DATA_DIR: Path = PROJECT_ROOT / os.getenv("DATA_DIR", "data")
    LOGS_DIR: Path = PROJECT_ROOT / os.getenv("LOGS_DIR", "logs")
    
    # 浏览器配置
    HEADLESS: bool = os.getenv("HEADLESS", "true").lower() == "true"
    BROWSER_TYPE: str = os.getenv("BROWSER_TYPE", "chromium")
    
    # 防反爬配置
    USE_PROXY: bool = os.getenv("USE_PROXY", "false").lower() == "true"
    PROXY_URL: Optional[str] = os.getenv("PROXY_URL")
    RANDOM_USER_AGENT: bool = os.getenv("RANDOM_USER_AGENT", "true").lower() == "true"
    RANDOM_DELAY: bool = os.getenv("RANDOM_DELAY", "true").lower() == "true"
    
    # 目标URL
    TARGET_URL: str = (
        "https://mobile.cmbchina.com/IEntrustFinance/financeproduct/financelist.html"
        "?_u=24&PrdTyp=A&behavior_entryid=LC0JGQ001&behavior_pageid=459C25E0"
        "&behavior_slotid=&appflag=1&_urlrefer=PLATFORM&DeviceType=E"
        "&Version=13.3.3&SystemVersion=11"
    )
    
    # 监听的网络请求关键字
    NETWORK_KEYWORDS: List[str] = [
        "alllist",
        "get-history-net-value.detail",
        "get-history-net-value",
        "get-history-performance",
        "get-history-profit",
        "get-seven-chart",
        "hist-rate-chart-z2",
        "million-chart",
        "net-value-chart",
        "prd-info"
    ]
    
    # 用户代理列表
    USER_AGENTS: List[str] = [
        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 13_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36"
    ]
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        cls.DATA_DIR.mkdir(parents=True, exist_ok=True)
        cls.LOGS_DIR.mkdir(parents=True, exist_ok=True)
        (cls.DATA_DIR / "html").mkdir(exist_ok=True)
        (cls.DATA_DIR / "json").mkdir(exist_ok=True)
        (cls.DATA_DIR / "network").mkdir(exist_ok=True)

# 创建配置实例
config = Config()
config.ensure_directories()
