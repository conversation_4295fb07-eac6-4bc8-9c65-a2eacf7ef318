#!/usr/bin/env python3
"""
数据分析脚本
"""
import json
import sys
from pathlib import Path
from collections import Counter
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import config


def load_latest_crawl_result():
    """加载最新的爬取结果"""
    json_dir = config.DATA_DIR / "json"
    if not json_dir.exists():
        print("❌ 未找到数据目录")
        return None
    
    # 查找最新的爬取结果文件
    result_files = list(json_dir.glob("crawl_result_*.json"))
    if not result_files:
        print("❌ 未找到爬取结果文件")
        return None
    
    latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
    print(f"📁 加载数据文件: {latest_file}")
    
    with open(latest_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def analyze_products(crawl_result):
    """分析产品数据"""
    products = crawl_result.get('products', [])
    if not products:
        print("❌ 没有产品数据")
        return
    
    print(f"\n📊 产品数据分析")
    print("=" * 50)
    print(f"总产品数量: {len(products)}")
    
    # 分析风险等级分布
    risk_levels = [p.get('risk_level') for p in products if p.get('risk_level')]
    if risk_levels:
        risk_counter = Counter(risk_levels)
        print(f"\n🎯 风险等级分布:")
        for risk, count in risk_counter.most_common():
            print(f"  {risk}: {count} 个")
    
    # 分析收益率分布
    returns = []
    for p in products:
        return_str = p.get('expected_return', '')
        if return_str and '%' in return_str:
            try:
                return_val = float(return_str.replace('%', ''))
                returns.append(return_val)
            except ValueError:
                pass
    
    if returns:
        print(f"\n💰 收益率统计:")
        print(f"  平均收益率: {sum(returns)/len(returns):.2f}%")
        print(f"  最高收益率: {max(returns):.2f}%")
        print(f"  最低收益率: {min(returns):.2f}%")
        print(f"  收益率样本数: {len(returns)}")
    
    # 分析起购金额
    amounts = []
    for p in products:
        amount_str = p.get('min_amount', '')
        if amount_str:
            # 简单提取数字
            import re
            numbers = re.findall(r'[\d,]+', amount_str)
            if numbers:
                try:
                    amount = int(numbers[0].replace(',', ''))
                    if '万' in amount_str:
                        amount *= 10000
                    amounts.append(amount)
                except ValueError:
                    pass
    
    if amounts:
        print(f"\n💵 起购金额统计:")
        print(f"  平均起购金额: {sum(amounts)/len(amounts):,.0f} 元")
        print(f"  最高起购金额: {max(amounts):,.0f} 元")
        print(f"  最低起购金额: {min(amounts):,.0f} 元")
        print(f"  起购金额样本数: {len(amounts)}")
    
    # 分析产品类型
    types = [p.get('product_type') for p in products if p.get('product_type')]
    if types:
        type_counter = Counter(types)
        print(f"\n📋 产品类型分布:")
        for ptype, count in type_counter.most_common():
            print(f"  {ptype}: {count} 个")
    
    # 分析产品状态
    statuses = [p.get('status') for p in products if p.get('status')]
    if statuses:
        status_counter = Counter(statuses)
        print(f"\n📈 产品状态分布:")
        for status, count in status_counter.most_common():
            print(f"  {status}: {count} 个")


def analyze_network_requests(crawl_result):
    """分析网络请求数据"""
    products = crawl_result.get('products', [])
    all_requests = []
    
    for product in products:
        requests = product.get('network_requests', [])
        all_requests.extend(requests)
    
    if not all_requests:
        print("\n❌ 没有网络请求数据")
        return
    
    print(f"\n🌐 网络请求分析")
    print("=" * 50)
    print(f"总请求数量: {len(all_requests)}")
    
    # 按URL关键字统计
    keyword_counts = {}
    keywords = config.NETWORK_KEYWORDS
    
    for request in all_requests:
        url = request.get('url', '')
        for keyword in keywords:
            if keyword in url:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
    
    if keyword_counts:
        print(f"\n🔍 关键字请求统计:")
        for keyword, count in sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {keyword}: {count} 次")
    
    # 按状态码统计
    status_codes = [r.get('status_code') for r in all_requests if r.get('status_code')]
    if status_codes:
        status_counter = Counter(status_codes)
        print(f"\n📊 状态码分布:")
        for status, count in status_counter.most_common():
            print(f"  {status}: {count} 次")


def generate_summary_report(crawl_result):
    """生成汇总报告"""
    print(f"\n📋 爬取汇总报告")
    print("=" * 50)
    
    # 基本统计
    total = crawl_result.get('total_products', 0)
    successful = crawl_result.get('successful_products', 0)
    failed = crawl_result.get('failed_products', 0)
    
    print(f"爬取时间: {crawl_result.get('crawl_start_time', 'N/A')}")
    print(f"完成时间: {crawl_result.get('crawl_end_time', 'N/A')}")
    print(f"总产品数: {total}")
    print(f"成功数量: {successful}")
    print(f"失败数量: {failed}")
    print(f"成功率: {successful/total*100:.1f}%" if total > 0 else "0%")
    
    # 错误信息
    errors = crawl_result.get('errors', [])
    if errors:
        print(f"\n❌ 错误信息 ({len(errors)} 个):")
        for i, error in enumerate(errors[:5], 1):  # 只显示前5个错误
            print(f"  {i}. {error}")
        if len(errors) > 5:
            print(f"  ... 还有 {len(errors) - 5} 个错误")


def main():
    """主函数"""
    print("招商银行理财产品数据分析")
    print("=" * 50)
    
    # 加载数据
    crawl_result = load_latest_crawl_result()
    if not crawl_result:
        return
    
    # 生成各种分析报告
    generate_summary_report(crawl_result)
    analyze_products(crawl_result)
    analyze_network_requests(crawl_result)
    
    print(f"\n✅ 分析完成")


if __name__ == "__main__":
    main()
