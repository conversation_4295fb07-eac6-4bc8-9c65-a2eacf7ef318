#!/usr/bin/env python3
"""
项目演示脚本 - 测试基本功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import config
from src.utils.anti_detection import AntiDetection
from src.utils.file_manager import FileManager
from src.utils.data_extractor import DataExtractor
from src.models.product import FinancialProduct, ProductDetails


def test_config():
    """测试配置模块"""
    print("🔧 测试配置模块...")
    print(f"  目标URL: {config.TARGET_URL}")
    print(f"  最大产品数: {config.MAX_PRODUCTS}")
    print(f"  并发限制: {config.CONCURRENT_LIMIT}")
    print(f"  数据目录: {config.DATA_DIR}")
    print(f"  日志目录: {config.LOGS_DIR}")
    print("✅ 配置模块测试通过")


def test_anti_detection():
    """测试防反爬模块"""
    print("\n🛡️ 测试防反爬模块...")
    
    anti_detection = AntiDetection()
    
    # 测试随机User-Agent
    ua1 = anti_detection.get_random_user_agent()
    ua2 = anti_detection.get_random_user_agent()
    print(f"  User-Agent 1: {ua1[:50]}...")
    print(f"  User-Agent 2: {ua2[:50]}...")
    
    # 测试随机请求头
    headers = anti_detection.get_random_headers()
    print(f"  请求头数量: {len(headers)}")
    print(f"  包含User-Agent: {'User-Agent' in headers}")
    
    # 测试浏览器选项
    browser_options = anti_detection.get_browser_options()
    print(f"  浏览器选项: {len(browser_options)} 个配置项")
    
    print("✅ 防反爬模块测试通过")


def test_file_manager():
    """测试文件管理模块"""
    print("\n📁 测试文件管理模块...")
    
    file_manager = FileManager()
    
    # 检查目录是否存在
    print(f"  数据目录存在: {file_manager.data_dir.exists()}")
    print(f"  HTML目录存在: {file_manager.html_dir.exists()}")
    print(f"  JSON目录存在: {file_manager.json_dir.exists()}")
    print(f"  网络目录存在: {file_manager.network_dir.exists()}")
    
    # 测试文件名生成
    filename = file_manager.generate_filename("test", "product_123")
    print(f"  生成的文件名: {filename}")
    
    print("✅ 文件管理模块测试通过")


def test_data_extractor():
    """测试数据提取模块"""
    print("\n🔍 测试数据提取模块...")
    
    extractor = DataExtractor()
    
    # 测试HTML文本提取
    html = """
    <html>
        <head><title>测试页面</title></head>
        <body>
            <h1>招银理财产品A</h1>
            <p>预期收益率: 4.2%</p>
            <p>风险等级: PR2</p>
            <p>起购金额: 1万元</p>
            <script>console.log('test');</script>
        </body>
    </html>
    """
    
    text = extractor.extract_text_from_html(html)
    print(f"  提取的文本长度: {len(text)}")
    print(f"  包含产品名称: {'招银理财产品A' in text}")
    print(f"  包含收益率: {'4.2%' in text}")
    
    # 测试基础信息提取
    info = extractor.extract_basic_info_from_html(html)
    print(f"  提取的信息: {info}")
    
    print("✅ 数据提取模块测试通过")


def test_data_models():
    """测试数据模型"""
    print("\n📊 测试数据模型...")
    
    # 创建产品详情
    details = ProductDetails(
        description="这是一个测试产品",
        features="低风险、稳健收益",
        investment_strategy="债券投资为主"
    )
    
    # 创建产品对象
    product = FinancialProduct(
        product_id="TEST_001",
        name="测试理财产品",
        expected_return="4.2%",
        risk_level="PR2",
        min_amount="1万元",
        details=details
    )
    
    print(f"  产品ID: {product.product_id}")
    print(f"  产品名称: {product.name}")
    print(f"  预期收益: {product.expected_return}")
    print(f"  风险等级: {product.risk_level}")
    print(f"  起购金额: {product.min_amount}")
    print(f"  爬取时间: {product.crawl_time}")
    
    # 测试JSON序列化
    json_data = product.model_dump()
    print(f"  JSON字段数: {len(json_data)}")
    
    print("✅ 数据模型测试通过")


async def test_async_functions():
    """测试异步功能"""
    print("\n⚡ 测试异步功能...")
    
    # 测试异步延迟
    anti_detection = AntiDetection()
    print("  测试随机延迟...")
    start_time = asyncio.get_event_loop().time()
    await anti_detection.random_delay(0.1, 0.2)
    end_time = asyncio.get_event_loop().time()
    delay_time = end_time - start_time
    print(f"  延迟时间: {delay_time:.2f}秒")
    
    # 测试文件管理器异步操作
    file_manager = FileManager()
    test_data = {"test": "data", "timestamp": "2024-01-01"}
    
    print("  测试异步JSON保存...")
    json_path = await file_manager.save_json(test_data, "test_demo")
    print(f"  保存路径: {json_path}")
    
    # 清理测试文件
    if json_path.exists():
        json_path.unlink()
        print("  测试文件已清理")
    
    print("✅ 异步功能测试通过")


def main():
    """主测试函数"""
    print("🚀 招商银行理财产品爬虫 - 功能演示")
    print("=" * 60)
    
    try:
        # 同步测试
        test_config()
        test_anti_detection()
        test_file_manager()
        test_data_extractor()
        test_data_models()
        
        # 异步测试
        asyncio.run(test_async_functions())
        
        print("\n" + "=" * 60)
        print("🎉 所有功能测试通过!")
        print("=" * 60)
        print("\n📋 下一步:")
        print("1. 配置.env文件中的API密钥")
        print("2. 运行完整爬虫: python run.py")
        print("3. 查看爬取结果: python analyze_data.py")
        print("4. 启动Web查看器: python web_viewer.py")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
