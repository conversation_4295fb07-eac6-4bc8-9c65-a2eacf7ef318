"""
招商银行理财产品爬虫
"""
import re
import json
import asyncio
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from loguru import logger
from src.crawler.base_crawler import BaseCrawler
from src.config.settings import config
from src.models.product import FinancialProduct, NetworkRequest
from datetime import datetime


class CMBCrawler(BaseCrawler):
    """招商银行理财产品爬虫"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://mobile.cmbchina.com"
        self.target_url = config.TARGET_URL
        
    async def crawl_product_list(self) -> List[Dict[str, Any]]:
        """爬取产品列表"""
        try:
            logger.info("开始爬取招商银行理财产品列表...")
            
            # 定义页面交互JavaScript代码
            js_code = """
            // 等待页面完全加载
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
            
            // 等待产品列表加载
            let retryCount = 0;
            while (retryCount < 10) {
                const productElements = document.querySelectorAll('[class*="product"], [class*="item"], li, .list-item');
                if (productElements.length > 0) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
                retryCount++;
            }
            
            // 滚动页面以加载更多产品
            let lastHeight = document.body.scrollHeight;
            let scrollCount = 0;
            
            while (scrollCount < 5) {
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                let newHeight = document.body.scrollHeight;
                if (newHeight === lastHeight) {
                    break;
                }
                lastHeight = newHeight;
                scrollCount++;
            }
            
            // 尝试点击"加载更多"按钮
            const loadMoreButtons = document.querySelectorAll('[class*="more"], [class*="load"], button');
            for (let button of loadMoreButtons) {
                if (button.textContent.includes('更多') || button.textContent.includes('加载')) {
                    button.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            
            // 最终滚动到顶部
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, 1000));
            """
            
            # 爬取列表页面
            result = await self.crawler.arun(
                url=self.target_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )
            
            if not result.success:
                logger.error(f"爬取产品列表失败: {result.error_message}")
                return []
            
            # 保存原始HTML
            await self.file_manager.save_html(
                result.html, 
                "product_list_original"
            )
            
            # 保存清洗后的HTML
            await self.file_manager.save_cleaned_html(
                result.html,
                "product_list"
            )
            
            # 从HTML中提取产品列表
            products = await self.data_extractor.extract_product_list_from_html(result.html)
            
            # 如果基础提取没有结果，尝试更详细的解析
            if not products:
                products = await self._parse_product_list_detailed(result.html)
            
            logger.info(f"从列表页面提取到{len(products)}个产品")
            
            # 保存产品列表JSON
            await self.file_manager.save_json(
                {"products": products, "total": len(products)},
                "product_list"
            )
            
            return products
            
        except Exception as e:
            logger.error(f"爬取产品列表异常: {e}")
            return []

    async def _parse_product_list_detailed(self, html_content: str) -> List[Dict[str, Any]]:
        """详细解析产品列表 - 基于实际HTML结构"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html_content, 'html.parser')
        products = []

        # 根据实际HTML结构，产品项在 li.sa-item.prd-list 中
        product_items = soup.select('li.sa-item.prd-list')

        if product_items:
            logger.info(f"找到 {len(product_items)} 个产品项")

            for i, item in enumerate(product_items):
                try:
                    product_info = self._extract_cmb_product_info(item, i)
                    if product_info and product_info.get('name'):
                        products.append(product_info)
                        logger.debug(f"成功解析产品: {product_info.get('name')}")
                except Exception as e:
                    logger.debug(f"解析产品项 {i} 失败: {e}")
        else:
            # 如果没有找到标准结构，尝试其他选择器
            logger.warning("未找到标准产品列表结构，尝试其他选择器...")
            fallback_selectors = [
                'li[data-behavior*="LCA"]',  # 基于data-behavior属性
                '.sa-item',                   # 基于class
                'li[class*="prd"]',          # 包含prd的li元素
                'li[class*="item"]'          # 包含item的li元素
            ]

            for selector in fallback_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用备用选择器 '{selector}' 找到 {len(elements)} 个元素")

                    for i, element in enumerate(elements):
                        try:
                            product_info = self._extract_cmb_product_info(element, i)
                            if product_info and product_info.get('name'):
                                products.append(product_info)
                        except Exception as e:
                            logger.debug(f"解析产品元素失败: {e}")

                    if products:
                        break

        # 去重
        seen_names = set()
        unique_products = []
        for product in products:
            name = product.get('name', '')
            if name and name not in seen_names:
                seen_names.add(name)
                unique_products.append(product)

        logger.info(f"解析完成，共获得 {len(unique_products)} 个唯一产品")
        return unique_products

    def _extract_cmb_product_info(self, element, index: int) -> Dict[str, Any]:
        """从招商银行产品元素中提取产品信息"""
        info = {
            'index': index,
            'element_html': str(element)[:500]  # 保存部分HTML用于调试
        }

        try:
            # 提取data-behavior属性作为产品ID的一部分
            behavior = element.get('data-behavior', '')
            if behavior:
                info['behavior_id'] = behavior

            # 提取产品名称 - 基于实际HTML结构
            name_elem = element.select_one('.fp-tb-title.name')
            if name_elem:
                info['name'] = name_elem.get_text(strip=True)

            # 提取管理机构信息
            com_elem = element.select_one('.fp-tb-title.com')
            if com_elem:
                com_text = com_elem.get_text(strip=True)
                # 提取管理机构名称，去掉"I 代销"前缀
                if '代销' in com_text:
                    info['issuer'] = com_text.replace('I 代销', '').strip()
                else:
                    info['issuer'] = com_text.replace('I ', '').strip()

            # 提取收益率信息
            rate_elem = element.select_one('.rate')
            if rate_elem:
                rate_text = rate_elem.get_text(strip=True)
                if rate_text and '%' in rate_text:
                    info['expected_return'] = rate_text

                    # 查找收益率类型（7日年化等）
                    rate_type_elem = rate_elem.find_next_sibling('h4')
                    if rate_type_elem:
                        info['return_type'] = rate_type_elem.get_text(strip=True)

            # 提取产品标签
            tag_elems = element.select('.fp_prdlabel_list')
            if tag_elems:
                tags = [tag.get_text(strip=True) for tag in tag_elems]
                info['tags'] = tags

                # 从标签中提取风险等级
                for tag in tags:
                    if 'R1' in tag or '低风险' in tag:
                        info['risk_level'] = 'R1'
                    elif 'R2' in tag or '中低风险' in tag:
                        info['risk_level'] = 'R2'
                    elif 'R3' in tag or '中风险' in tag:
                        info['risk_level'] = 'R3'
                    elif 'R4' in tag or '中高风险' in tag:
                        info['risk_level'] = 'R4'
                    elif 'R5' in tag or '高风险' in tag:
                        info['risk_level'] = 'R5'

            # 提取交易信息
            trade_info_elems = element.select('.ef-tb-r h3, .ef-tb-r h4')
            trade_info = []
            for elem in trade_info_elems:
                text = elem.get_text(strip=True)
                if text:
                    trade_info.append(text)

            if trade_info:
                info['trade_info'] = trade_info
                # 尝试从交易信息中提取到账时间
                for text in trade_info:
                    if '到账' in text:
                        info['arrival_time'] = text
                    elif '享收益' in text:
                        info['income_start'] = text

            # 生成产品ID
            if behavior:
                info['product_id'] = behavior
            elif info.get('name'):
                # 使用产品名称生成ID
                name_hash = hash(info['name']) % 100000
                info['product_id'] = f"CMB_{name_hash:05d}"
            else:
                info['product_id'] = f"CMB_{index:05d}"

            # 设置产品状态（从HTML结构推断，通常列表中的都是在售状态）
            info['status'] = '在售'

            # 设置产品类型（从管理机构推断）
            if info.get('issuer'):
                if '理财' in info['issuer']:
                    info['product_type'] = '银行理财'
                else:
                    info['product_type'] = '理财产品'

            # 设置币种（默认人民币，除非有特殊标识）
            info['currency'] = 'CNY'

        except Exception as e:
            logger.error(f"解析产品信息时出错: {e}")
            # 确保至少有基本信息
            if not info.get('name'):
                # 尝试从整个元素文本中提取名称
                text = element.get_text(strip=True)
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                if lines:
                    # 通常第一行或最长的行是产品名称
                    potential_names = [line for line in lines if len(line) > 3 and not line.isdigit()]
                    if potential_names:
                        info['name'] = potential_names[0]

            if not info.get('product_id'):
                info['product_id'] = f"CMB_{index:05d}"

        return info

    async def crawl_product_detail(self, product_info: Dict[str, Any]) -> FinancialProduct:
        """爬取产品详情"""
        product_id = product_info.get('product_id', 'unknown')
        product_name = product_info.get('name', '未知产品')

        try:
            logger.info(f"开始爬取产品详情: {product_name} ({product_id})")

            # 清空网络监听器
            self.network_monitor.clear_captured_requests()

            # 如果有详情页URL，直接访问
            detail_url = product_info.get('detail_url')
            if detail_url:
                detail_result = await self._crawl_product_detail_page(detail_url, product_id)
            else:
                # 如果没有详情页URL，尝试通过产品列表页面的交互获取
                detail_result = await self._crawl_product_detail_from_list(product_info)

            if not detail_result or not detail_result.get('success'):
                logger.warning(f"无法获取产品详情页面: {product_name}")
                # 使用基础信息创建产品对象
                return await self._create_product_from_basic_info(product_info)

            # 从详情页面提取产品信息
            product = await self.data_extractor.extract_product_from_html(
                detail_result['html'],
                product_id
            )

            # 补充基础信息
            if not product.name or product.name == '未知产品':
                product.name = product_name

            # 添加网络请求记录
            captured_requests = self.network_monitor.get_captured_requests()
            product.network_requests.extend(captured_requests)

            # 设置来源URL
            product.source_url = self.target_url
            product.detail_url = detail_url

            # 保存详情页面HTML
            await self.file_manager.save_html(
                detail_result['html'],
                f"product_detail_{product_id}",
                "products"
            )

            # 保存清洗后的HTML
            await self.file_manager.save_cleaned_html(
                detail_result['html'],
                f"product_detail_{product_id}",
                "products"
            )

            logger.info(f"产品详情爬取完成: {product_name}")
            return product

        except Exception as e:
            logger.error(f"爬取产品详情异常 {product_name}: {e}")
            # 返回基础产品信息
            return await self._create_product_from_basic_info(product_info)

    async def _crawl_product_detail_page(self, detail_url: str, product_id: str) -> Dict[str, Any]:
        """爬取产品详情页面"""
        try:
            # 设置网络监听
            js_code = f"""
            // 监听网络请求
            const originalFetch = window.fetch;
            const originalXHR = window.XMLHttpRequest.prototype.open;

            window.fetch = function(...args) {{
                console.log('Fetch request:', args[0]);
                return originalFetch.apply(this, args);
            }};

            // 等待页面加载
            await new Promise(resolve => {{
                if (document.readyState === 'complete') {{
                    resolve();
                }} else {{
                    window.addEventListener('load', resolve);
                }}
            }});

            // 滚动页面以触发懒加载和网络请求
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 尝试点击各种按钮来触发更多请求
            const buttons = document.querySelectorAll('button, [class*="btn"], [onclick]');
            for (let i = 0; i < Math.min(buttons.length, 5); i++) {{
                try {{
                    buttons[i].click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }} catch (e) {{
                    console.log('Button click failed:', e);
                }}
            }}

            // 滚动回顶部
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, 1000));
            """

            result = await self.crawler.arun(
                url=detail_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"爬取详情页面异常 {detail_url}: {e}")
            return {'success': False, 'error': str(e)}

    async def _crawl_product_detail_from_list(self, product_info: Dict[str, Any]) -> Dict[str, Any]:
        """从产品列表页面通过交互获取详情"""
        try:
            # 这里可以实现通过JavaScript在列表页面点击产品来获取详情
            # 由于具体的交互方式取决于页面结构，这里提供一个基础框架

            product_name = product_info.get('name', '')

            js_code = f"""
            // 查找包含产品名称的元素
            const productElements = Array.from(document.querySelectorAll('*')).filter(el =>
                el.textContent.includes('{product_name}') &&
                (el.tagName === 'A' || el.onclick || el.getAttribute('href'))
            );

            if (productElements.length > 0) {{
                const element = productElements[0];
                if (element.tagName === 'A' && element.href) {{
                    window.location.href = element.href;
                }} else if (element.onclick) {{
                    element.click();
                }} else {{
                    // 尝试找到父级链接
                    const parentLink = element.closest('a');
                    if (parentLink && parentLink.href) {{
                        window.location.href = parentLink.href;
                    }}
                }}

                // 等待页面跳转或内容加载
                await new Promise(resolve => setTimeout(resolve, 3000));
            }}
            """

            result = await self.crawler.arun(
                url=self.target_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"从列表页面获取详情失败: {e}")
            return {'success': False, 'error': str(e)}

    async def _create_product_from_basic_info(self, product_info: Dict[str, Any]) -> FinancialProduct:
        """从基础信息创建产品对象"""
        product_id = product_info.get('product_id', f"CMB_{hash(str(product_info)) % 100000:05d}")

        product = FinancialProduct(
            product_id=product_id,
            name=product_info.get('name', '未知产品'),
            product_code=product_info.get('product_code'),
            expected_return=product_info.get('expected_return'),
            risk_level=product_info.get('risk_level'),
            min_amount=product_info.get('min_amount'),
            status=product_info.get('status'),
            source_url=self.target_url,
            detail_url=product_info.get('detail_url'),
            issuer="招商银行"
        )

        return product

    async def crawl_with_network_monitoring(self, url: str) -> Dict[str, Any]:
        """带网络监听的爬取"""
        try:
            # 清空之前的请求记录
            self.network_monitor.clear_captured_requests()

            # 使用自定义的网络监听JavaScript
            js_code = """
            // 拦截fetch请求
            const originalFetch = window.fetch;
            window.capturedRequests = [];

            window.fetch = function(url, options = {}) {
                console.log('Intercepted fetch:', url);
                window.capturedRequests.push({
                    url: url,
                    method: options.method || 'GET',
                    timestamp: new Date().toISOString()
                });
                return originalFetch.apply(this, arguments);
            };

            // 拦截XMLHttpRequest
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url) {
                console.log('Intercepted XHR:', method, url);
                window.capturedRequests.push({
                    url: url,
                    method: method,
                    timestamp: new Date().toISOString()
                });
                return originalXHROpen.apply(this, arguments);
            };

            // 等待页面加载并触发网络请求
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });

            // 滚动和交互以触发更多请求
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 点击可能触发网络请求的元素
            const clickableElements = document.querySelectorAll('button, [onclick], .btn, [class*="click"]');
            for (let i = 0; i < Math.min(clickableElements.length, 3); i++) {
                try {
                    clickableElements[i].click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (e) {
                    console.log('Click failed:', e);
                }
            }

            await new Promise(resolve => setTimeout(resolve, 2000));
            """

            result = await self.crawler.arun(
                url=url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"带网络监听的爬取失败 {url}: {e}")
            return {'success': False, 'error': str(e)}
