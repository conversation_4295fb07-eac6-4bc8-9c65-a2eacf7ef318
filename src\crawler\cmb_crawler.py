"""
招商银行理财产品爬虫
"""
import re
import json
import asyncio
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from loguru import logger
from src.crawler.base_crawler import BaseCrawler
from src.config.settings import config
from src.models.product import FinancialProduct, NetworkRequest
from datetime import datetime


class CMBCrawler(BaseCrawler):
    """招商银行理财产品爬虫"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://mobile.cmbchina.com"
        self.target_url = config.TARGET_URL
        
    async def crawl_product_list(self) -> List[Dict[str, Any]]:
        """爬取产品列表"""
        try:
            logger.info("开始爬取招商银行理财产品列表...")
            
            # 定义页面交互JavaScript代码
            js_code = """
            // 等待页面完全加载
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
            
            // 等待产品列表加载
            let retryCount = 0;
            while (retryCount < 10) {
                const productElements = document.querySelectorAll('[class*="product"], [class*="item"], li, .list-item');
                if (productElements.length > 0) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
                retryCount++;
            }
            
            // 滚动页面以加载更多产品
            let lastHeight = document.body.scrollHeight;
            let scrollCount = 0;
            
            while (scrollCount < 5) {
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                let newHeight = document.body.scrollHeight;
                if (newHeight === lastHeight) {
                    break;
                }
                lastHeight = newHeight;
                scrollCount++;
            }
            
            // 尝试点击"加载更多"按钮
            const loadMoreButtons = document.querySelectorAll('[class*="more"], [class*="load"], button');
            for (let button of loadMoreButtons) {
                if (button.textContent.includes('更多') || button.textContent.includes('加载')) {
                    button.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            
            // 最终滚动到顶部
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, 1000));
            """
            
            # 爬取列表页面
            result = await self.crawler.arun(
                url=self.target_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )
            
            if not result.success:
                logger.error(f"爬取产品列表失败: {result.error_message}")
                return []
            
            # 保存原始HTML
            await self.file_manager.save_html(
                result.html, 
                "product_list_original"
            )
            
            # 保存清洗后的HTML
            await self.file_manager.save_cleaned_html(
                result.html,
                "product_list"
            )
            
            # 从HTML中提取产品列表
            products = await self.data_extractor.extract_product_list_from_html(result.html)
            
            # 如果基础提取没有结果，尝试更详细的解析
            if not products:
                products = await self._parse_product_list_detailed(result.html)
            
            logger.info(f"从列表页面提取到{len(products)}个产品")
            
            # 保存产品列表JSON
            await self.file_manager.save_json(
                {"products": products, "total": len(products)},
                "product_list"
            )
            
            return products
            
        except Exception as e:
            logger.error(f"爬取产品列表异常: {e}")
            return []

    async def _parse_product_list_detailed(self, html_content: str) -> List[Dict[str, Any]]:
        """详细解析产品列表"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html_content, 'html.parser')
        products = []

        # 尝试多种选择器来找到产品项
        selectors = [
            '[class*="product"]',
            '[class*="item"]',
            '[class*="list"] li',
            'li',
            '.item',
            '[data-product]',
            '[onclick*="product"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")

                for i, element in enumerate(elements):
                    try:
                        product_info = self._extract_product_info_from_element(element, i)
                        if product_info and product_info.get('name'):
                            products.append(product_info)
                    except Exception as e:
                        logger.debug(f"解析产品元素失败: {e}")

                if products:
                    break

        # 去重
        seen_names = set()
        unique_products = []
        for product in products:
            name = product.get('name', '')
            if name and name not in seen_names:
                seen_names.add(name)
                unique_products.append(product)

        return unique_products

    def _extract_product_info_from_element(self, element, index: int) -> Dict[str, Any]:
        """从单个元素中提取产品信息"""
        info = {
            'index': index,
            'element_html': str(element)[:500]  # 保存部分HTML用于调试
        }

        text = element.get_text(strip=True)

        # 提取产品名称
        name_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', 'strong', 'b']
        for selector in name_selectors:
            name_elem = element.select_one(selector)
            if name_elem:
                name = name_elem.get_text(strip=True)
                if len(name) > 5:  # 产品名称通常较长
                    info['name'] = name
                    break

        # 如果没找到名称，使用最长的文本作为名称
        if not info.get('name'):
            text_parts = [t.strip() for t in text.split('\n') if t.strip()]
            if text_parts:
                longest_text = max(text_parts, key=len)
                if len(longest_text) > 5:
                    info['name'] = longest_text

        # 提取收益率
        return_patterns = [
            r'(\d+\.?\d*%)',
            r'收益率[：:]\s*(\d+\.?\d*%?)',
            r'年化[：:]\s*(\d+\.?\d*%?)'
        ]
        for pattern in return_patterns:
            match = re.search(pattern, text)
            if match:
                info['expected_return'] = match.group(1)
                break

        # 提取风险等级
        risk_patterns = [
            r'([PR]\d+)',
            r'风险等级[：:]\s*([PR]\d+)',
            r'(低风险|中风险|高风险)'
        ]
        for pattern in risk_patterns:
            match = re.search(pattern, text)
            if match:
                info['risk_level'] = match.group(1)
                break

        # 提取起购金额
        amount_patterns = [
            r'起购[：:]\s*([\d,]+(?:\.\d+)?(?:万)?元?)',
            r'最低[：:]\s*([\d,]+(?:\.\d+)?(?:万)?元?)',
            r'([\d,]+万?元起)'
        ]
        for pattern in amount_patterns:
            match = re.search(pattern, text)
            if match:
                info['min_amount'] = match.group(1)
                break

        # 尝试提取产品链接
        link_elem = element.find('a')
        if link_elem and link_elem.get('href'):
            href = link_elem.get('href')
            if href.startswith('/'):
                info['detail_url'] = urljoin(self.base_url, href)
            elif href.startswith('http'):
                info['detail_url'] = href

        # 尝试从onclick事件中提取产品ID
        onclick = element.get('onclick', '')
        if onclick:
            # 查找产品ID模式
            id_match = re.search(r'["\']([A-Z0-9]{6,})["\']', onclick)
            if id_match:
                info['product_id'] = id_match.group(1)

        # 生成产品ID（如果没有找到）
        if not info.get('product_id'):
            name = info.get('name', '')
            if name:
                info['product_id'] = f"CMB_{hash(name) % 100000:05d}"
            else:
                info['product_id'] = f"CMB_{index:05d}"

        return info

    async def crawl_product_detail(self, product_info: Dict[str, Any]) -> FinancialProduct:
        """爬取产品详情"""
        product_id = product_info.get('product_id', 'unknown')
        product_name = product_info.get('name', '未知产品')

        try:
            logger.info(f"开始爬取产品详情: {product_name} ({product_id})")

            # 清空网络监听器
            self.network_monitor.clear_captured_requests()

            # 如果有详情页URL，直接访问
            detail_url = product_info.get('detail_url')
            if detail_url:
                detail_result = await self._crawl_product_detail_page(detail_url, product_id)
            else:
                # 如果没有详情页URL，尝试通过产品列表页面的交互获取
                detail_result = await self._crawl_product_detail_from_list(product_info)

            if not detail_result or not detail_result.get('success'):
                logger.warning(f"无法获取产品详情页面: {product_name}")
                # 使用基础信息创建产品对象
                return await self._create_product_from_basic_info(product_info)

            # 从详情页面提取产品信息
            product = await self.data_extractor.extract_product_from_html(
                detail_result['html'],
                product_id
            )

            # 补充基础信息
            if not product.name or product.name == '未知产品':
                product.name = product_name

            # 添加网络请求记录
            captured_requests = self.network_monitor.get_captured_requests()
            product.network_requests.extend(captured_requests)

            # 设置来源URL
            product.source_url = self.target_url
            product.detail_url = detail_url

            # 保存详情页面HTML
            await self.file_manager.save_html(
                detail_result['html'],
                f"product_detail_{product_id}",
                "products"
            )

            # 保存清洗后的HTML
            await self.file_manager.save_cleaned_html(
                detail_result['html'],
                f"product_detail_{product_id}",
                "products"
            )

            logger.info(f"产品详情爬取完成: {product_name}")
            return product

        except Exception as e:
            logger.error(f"爬取产品详情异常 {product_name}: {e}")
            # 返回基础产品信息
            return await self._create_product_from_basic_info(product_info)

    async def _crawl_product_detail_page(self, detail_url: str, product_id: str) -> Dict[str, Any]:
        """爬取产品详情页面"""
        try:
            # 设置网络监听
            js_code = f"""
            // 监听网络请求
            const originalFetch = window.fetch;
            const originalXHR = window.XMLHttpRequest.prototype.open;

            window.fetch = function(...args) {{
                console.log('Fetch request:', args[0]);
                return originalFetch.apply(this, args);
            }};

            // 等待页面加载
            await new Promise(resolve => {{
                if (document.readyState === 'complete') {{
                    resolve();
                }} else {{
                    window.addEventListener('load', resolve);
                }}
            }});

            // 滚动页面以触发懒加载和网络请求
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 尝试点击各种按钮来触发更多请求
            const buttons = document.querySelectorAll('button, [class*="btn"], [onclick]');
            for (let i = 0; i < Math.min(buttons.length, 5); i++) {{
                try {{
                    buttons[i].click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }} catch (e) {{
                    console.log('Button click failed:', e);
                }}
            }}

            // 滚动回顶部
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, 1000));
            """

            result = await self.crawler.arun(
                url=detail_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"爬取详情页面异常 {detail_url}: {e}")
            return {'success': False, 'error': str(e)}

    async def _crawl_product_detail_from_list(self, product_info: Dict[str, Any]) -> Dict[str, Any]:
        """从产品列表页面通过交互获取详情"""
        try:
            # 这里可以实现通过JavaScript在列表页面点击产品来获取详情
            # 由于具体的交互方式取决于页面结构，这里提供一个基础框架

            product_name = product_info.get('name', '')

            js_code = f"""
            // 查找包含产品名称的元素
            const productElements = Array.from(document.querySelectorAll('*')).filter(el =>
                el.textContent.includes('{product_name}') &&
                (el.tagName === 'A' || el.onclick || el.getAttribute('href'))
            );

            if (productElements.length > 0) {{
                const element = productElements[0];
                if (element.tagName === 'A' && element.href) {{
                    window.location.href = element.href;
                }} else if (element.onclick) {{
                    element.click();
                }} else {{
                    // 尝试找到父级链接
                    const parentLink = element.closest('a');
                    if (parentLink && parentLink.href) {{
                        window.location.href = parentLink.href;
                    }}
                }}

                // 等待页面跳转或内容加载
                await new Promise(resolve => setTimeout(resolve, 3000));
            }}
            """

            result = await self.crawler.arun(
                url=self.target_url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"从列表页面获取详情失败: {e}")
            return {'success': False, 'error': str(e)}

    async def _create_product_from_basic_info(self, product_info: Dict[str, Any]) -> FinancialProduct:
        """从基础信息创建产品对象"""
        product_id = product_info.get('product_id', f"CMB_{hash(str(product_info)) % 100000:05d}")

        product = FinancialProduct(
            product_id=product_id,
            name=product_info.get('name', '未知产品'),
            product_code=product_info.get('product_code'),
            expected_return=product_info.get('expected_return'),
            risk_level=product_info.get('risk_level'),
            min_amount=product_info.get('min_amount'),
            status=product_info.get('status'),
            source_url=self.target_url,
            detail_url=product_info.get('detail_url'),
            issuer="招商银行"
        )

        return product

    async def crawl_with_network_monitoring(self, url: str) -> Dict[str, Any]:
        """带网络监听的爬取"""
        try:
            # 清空之前的请求记录
            self.network_monitor.clear_captured_requests()

            # 使用自定义的网络监听JavaScript
            js_code = """
            // 拦截fetch请求
            const originalFetch = window.fetch;
            window.capturedRequests = [];

            window.fetch = function(url, options = {}) {
                console.log('Intercepted fetch:', url);
                window.capturedRequests.push({
                    url: url,
                    method: options.method || 'GET',
                    timestamp: new Date().toISOString()
                });
                return originalFetch.apply(this, arguments);
            };

            // 拦截XMLHttpRequest
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url) {
                console.log('Intercepted XHR:', method, url);
                window.capturedRequests.push({
                    url: url,
                    method: method,
                    timestamp: new Date().toISOString()
                });
                return originalXHROpen.apply(this, arguments);
            };

            // 等待页面加载并触发网络请求
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });

            // 滚动和交互以触发更多请求
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 点击可能触发网络请求的元素
            const clickableElements = document.querySelectorAll('button, [onclick], .btn, [class*="click"]');
            for (let i = 0; i < Math.min(clickableElements.length, 3); i++) {
                try {
                    clickableElements[i].click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (e) {
                    console.log('Click failed:', e);
                }
            }

            await new Promise(resolve => setTimeout(resolve, 2000));
            """

            result = await self.crawler.arun(
                url=url,
                js_code=js_code,
                wait_for="body",
                page_timeout=config.TIMEOUT * 1000,
                delay_before_return_html=3.0,
                simulate_user=True
            )

            return result.__dict__ if hasattr(result, '__dict__') else {
                'success': result.success,
                'html': result.html if result.success else '',
                'error': result.error_message if not result.success else None
            }

        except Exception as e:
            logger.error(f"带网络监听的爬取失败 {url}: {e}")
            return {'success': False, 'error': str(e)}
