#!/usr/bin/env python3
"""
项目安装脚本
"""
import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.9或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def install_uv():
    """安装uv包管理器"""
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        print("✅ uv已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("🔄 安装uv包管理器...")
        return run_command("pip install uv", "安装uv")


def setup_virtual_environment():
    """设置虚拟环境"""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("✅ 虚拟环境已存在")
    else:
        if not run_command("uv venv", "创建虚拟环境"):
            return False
    
    # 安装项目依赖
    return run_command("uv pip install -e .", "安装项目依赖")


def setup_environment_file():
    """设置环境变量文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env文件已存在")
        return True
    
    if env_example.exists():
        try:
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 已创建.env文件（从.env.example复制）")
            print("⚠️  请编辑.env文件配置API密钥等参数")
            return True
        except Exception as e:
            print(f"❌ 复制.env文件失败: {e}")
            return False
    else:
        print("❌ 未找到.env.example文件")
        return False


def install_playwright():
    """安装Playwright浏览器"""
    print("🔄 安装Playwright浏览器...")
    commands = [
        "uv pip install playwright",
        "playwright install chromium"
    ]
    
    for cmd in commands:
        if not run_command(cmd, f"执行: {cmd}"):
            return False
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = ["data", "data/html", "data/json", "data/network", "logs"]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")
    
    return True


def run_tests():
    """运行测试"""
    print("🔄 运行测试...")
    return run_command("python -m pytest tests/ -v", "运行测试")


def main():
    """主安装流程"""
    print("🚀 招商银行理财产品爬虫 - 安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装uv
    if not install_uv():
        print("❌ 无法安装uv，请手动安装: pip install uv")
        sys.exit(1)
    
    # 设置虚拟环境
    if not setup_virtual_environment():
        print("❌ 虚拟环境设置失败")
        sys.exit(1)
    
    # 设置环境文件
    if not setup_environment_file():
        print("❌ 环境文件设置失败")
        sys.exit(1)
    
    # 安装Playwright
    if not install_playwright():
        print("⚠️  Playwright安装失败，可能影响浏览器功能")
    
    # 创建目录
    if not create_directories():
        print("❌ 目录创建失败")
        sys.exit(1)
    
    # 运行测试
    if not run_tests():
        print("⚠️  测试失败，但安装可能仍然成功")
    
    print("\n" + "=" * 50)
    print("🎉 安装完成!")
    print("=" * 50)
    print("\n📋 下一步:")
    print("1. 编辑 .env 文件配置API密钥")
    print("2. 运行爬虫: python run.py")
    print("3. 查看数据: python analyze_data.py")
    print("4. Web查看器: python web_viewer.py")
    print("\n📚 更多信息请查看 README.md")


if __name__ == "__main__":
    main()
