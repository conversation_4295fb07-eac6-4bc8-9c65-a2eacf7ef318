#!/usr/bin/env python3
"""
简单的Web查看器，用于查看爬取的数据
"""
import json
import sys
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import webbrowser
import threading
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import config


class DataViewerHandler(SimpleHTTPRequestHandler):
    """数据查看器处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_index()
        elif parsed_path.path == '/api/products':
            self.serve_products_api()
        elif parsed_path.path == '/api/latest':
            self.serve_latest_result()
        else:
            super().do_GET()
    
    def serve_index(self):
        """提供主页"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>招商银行理财产品数据查看器</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .stats { display: flex; gap: 20px; margin: 20px 0; }
                .stat-card { background: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; }
                .products { margin-top: 20px; }
                .product { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
                .product-name { font-weight: bold; color: #2c5aa0; }
                .product-info { margin: 5px 0; }
                .loading { text-align: center; padding: 20px; }
                .error { color: red; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🏦 招商银行理财产品数据查看器</h1>
                <p>查看最新爬取的理财产品数据</p>
            </div>
            
            <div id="loading" class="loading">正在加载数据...</div>
            <div id="error" class="error" style="display: none;"></div>
            <div id="content" style="display: none;">
                <div class="stats">
                    <div class="stat-card">
                        <h3>📊 总计</h3>
                        <p id="total-products">-</p>
                    </div>
                    <div class="stat-card">
                        <h3>✅ 成功</h3>
                        <p id="successful-products">-</p>
                    </div>
                    <div class="stat-card">
                        <h3>❌ 失败</h3>
                        <p id="failed-products">-</p>
                    </div>
                    <div class="stat-card">
                        <h3>📈 成功率</h3>
                        <p id="success-rate">-</p>
                    </div>
                </div>
                
                <h2>💰 产品列表</h2>
                <table id="products-table">
                    <thead>
                        <tr>
                            <th>产品名称</th>
                            <th>产品ID</th>
                            <th>预期收益率</th>
                            <th>风险等级</th>
                            <th>起购金额</th>
                            <th>产品状态</th>
                        </tr>
                    </thead>
                    <tbody id="products-tbody">
                    </tbody>
                </table>
            </div>
            
            <script>
                async function loadData() {
                    try {
                        const response = await fetch('/api/latest');
                        const data = await response.json();
                        
                        if (data.error) {
                            showError(data.error);
                            return;
                        }
                        
                        displayData(data);
                    } catch (error) {
                        showError('加载数据失败: ' + error.message);
                    }
                }
                
                function showError(message) {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('error').textContent = message;
                }
                
                function displayData(data) {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                    
                    // 显示统计信息
                    document.getElementById('total-products').textContent = data.total_products || 0;
                    document.getElementById('successful-products').textContent = data.successful_products || 0;
                    document.getElementById('failed-products').textContent = data.failed_products || 0;
                    
                    const successRate = data.total_products > 0 ? 
                        (data.successful_products / data.total_products * 100).toFixed(1) + '%' : '0%';
                    document.getElementById('success-rate').textContent = successRate;
                    
                    // 显示产品列表
                    const tbody = document.getElementById('products-tbody');
                    tbody.innerHTML = '';
                    
                    (data.products || []).forEach(product => {
                        const row = tbody.insertRow();
                        row.insertCell(0).textContent = product.name || '-';
                        row.insertCell(1).textContent = product.product_id || '-';
                        row.insertCell(2).textContent = product.expected_return || '-';
                        row.insertCell(3).textContent = product.risk_level || '-';
                        row.insertCell(4).textContent = product.min_amount || '-';
                        row.insertCell(5).textContent = product.status || '-';
                    });
                }
                
                // 页面加载时获取数据
                loadData();
                
                // 每30秒刷新一次数据
                setInterval(loadData, 30000);
            </script>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_latest_result(self):
        """提供最新的爬取结果API"""
        try:
            json_dir = config.DATA_DIR / "json"
            if not json_dir.exists():
                self.send_json_response({'error': '数据目录不存在'})
                return
            
            # 查找最新的爬取结果文件
            result_files = list(json_dir.glob("crawl_result_*.json"))
            if not result_files:
                self.send_json_response({'error': '未找到爬取结果文件'})
                return
            
            latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.send_json_response(data)
            
        except Exception as e:
            self.send_json_response({'error': f'加载数据失败: {str(e)}'})
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        json_str = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_str.encode('utf-8'))


def start_server(port=8000):
    """启动服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, DataViewerHandler)
    
    print(f"🌐 启动Web查看器服务器...")
    print(f"📍 地址: http://localhost:{port}")
    print(f"🔗 请在浏览器中打开上述地址查看数据")
    print(f"⏹️  按 Ctrl+C 停止服务器")
    
    # 自动打开浏览器
    def open_browser():
        time.sleep(1)  # 等待服务器启动
        webbrowser.open(f'http://localhost:{port}')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n⏹️  服务器已停止")
        httpd.shutdown()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='启动数据查看器Web服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口 (默认: 8000)')
    
    args = parser.parse_args()
    
    start_server(args.port)
