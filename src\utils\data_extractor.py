"""
数据提取工具 - 使用DeepSeek进行数据提取和结构化
"""
import json
import re
from typing import Dict, List, Optional, Any
from openai import AsyncOpenAI
from loguru import logger
from bs4 import BeautifulSoup
from src.config.settings import config
from src.models.product import FinancialProduct, ProductDetails


class DataExtractor:
    """数据提取器"""
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=config.DEEPSEEK_API_KEY,
            base_url=config.DEEPSEEK_BASE_URL
        ) if config.DEEPSEEK_API_KEY else None
    
    def extract_text_from_html(self, html_content: str) -> str:
        """从HTML中提取纯文本"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 移除脚本和样式
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 获取文本
        text = soup.get_text()
        
        # 清理文本
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def extract_basic_info_from_html(self, html_content: str) -> Dict[str, Any]:
        """从HTML中提取基础产品信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        info = {}
        
        try:
            # 尝试提取产品名称
            title_selectors = [
                'h1', 'h2', '.title', '.product-name', 
                '[class*="title"]', '[class*="name"]'
            ]
            for selector in title_selectors:
                element = soup.select_one(selector)
                if element and element.get_text().strip():
                    info['name'] = element.get_text().strip()
                    break
            
            # 尝试提取产品代码
            code_patterns = [
                r'产品代码[：:]\s*([A-Z0-9]+)',
                r'代码[：:]\s*([A-Z0-9]+)',
                r'编号[：:]\s*([A-Z0-9]+)'
            ]
            text = self.extract_text_from_html(html_content)
            for pattern in code_patterns:
                match = re.search(pattern, text)
                if match:
                    info['product_code'] = match.group(1)
                    break
            
            # 尝试提取收益率
            return_patterns = [
                r'预期收益率[：:]\s*([\d.]+%?)',
                r'年化收益率[：:]\s*([\d.]+%?)',
                r'收益率[：:]\s*([\d.]+%?)'
            ]
            for pattern in return_patterns:
                match = re.search(pattern, text)
                if match:
                    info['expected_return'] = match.group(1)
                    break
            
            # 尝试提取风险等级
            risk_patterns = [
                r'风险等级[：:]\s*([PR\d]+)',
                r'风险级别[：:]\s*([PR\d]+)',
                r'风险[：:]\s*([低中高]+风险)'
            ]
            for pattern in risk_patterns:
                match = re.search(pattern, text)
                if match:
                    info['risk_level'] = match.group(1)
                    break
            
            # 尝试提取起购金额
            amount_patterns = [
                r'起购金额[：:]\s*([\d,]+(?:\.\d+)?(?:万)?元?)',
                r'最低投资[：:]\s*([\d,]+(?:\.\d+)?(?:万)?元?)',
                r'起投金额[：:]\s*([\d,]+(?:\.\d+)?(?:万)?元?)'
            ]
            for pattern in amount_patterns:
                match = re.search(pattern, text)
                if match:
                    info['min_amount'] = match.group(1)
                    break
            
        except Exception as e:
            logger.error(f"HTML信息提取失败: {e}")
        
        return info
    
    async def extract_with_ai(self, html_content: str, 
                            extraction_type: str = "product") -> Dict[str, Any]:
        """使用AI提取结构化数据"""
        if not self.client:
            logger.warning("DeepSeek API未配置，跳过AI提取")
            return {}
        
        try:
            # 清理HTML并提取文本
            text = self.extract_text_from_html(html_content)
            
            # 限制文本长度
            if len(text) > 4000:
                text = text[:4000] + "..."
            
            if extraction_type == "product":
                prompt = self._get_product_extraction_prompt(text)
            elif extraction_type == "product_list":
                prompt = self._get_product_list_extraction_prompt(text)
            else:
                prompt = self._get_general_extraction_prompt(text)
            
            response = await self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的金融数据提取专家。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    logger.warning("AI响应中未找到JSON格式数据")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"AI响应JSON解析失败: {e}")
                return {}
                
        except Exception as e:
            logger.error(f"AI数据提取失败: {e}")
            return {}
    
    def _get_product_extraction_prompt(self, text: str) -> str:
        """获取产品信息提取提示词"""
        return f"""
请从以下银行理财产品页面文本中提取结构化信息，返回JSON格式：

{text}

请提取以下字段（如果存在）：
- product_id: 产品唯一标识或代码
- name: 产品名称
- product_code: 产品代码
- product_type: 产品类型
- risk_level: 风险等级
- expected_return: 预期收益率
- investment_period: 投资期限
- min_amount: 起购金额
- status: 产品状态
- sale_start_date: 销售起始日
- sale_end_date: 销售结束日
- currency: 币种
- issuer: 发行机构
- description: 产品描述
- features: 产品特色
- investment_strategy: 投资策略

返回格式：
{{
  "product_id": "...",
  "name": "...",
  ...
}}
"""
    
    def _get_product_list_extraction_prompt(self, text: str) -> str:
        """获取产品列表提取提示词"""
        return f"""
请从以下银行理财产品列表页面文本中提取所有产品的基础信息，返回JSON数组格式：

{text}

对于每个产品，请提取：
- product_id: 产品标识
- name: 产品名称
- expected_return: 预期收益率
- risk_level: 风险等级
- min_amount: 起购金额
- status: 产品状态

返回格式：
{{
  "products": [
    {{
      "product_id": "...",
      "name": "...",
      ...
    }}
  ]
}}
"""
    
    def _get_general_extraction_prompt(self, text: str) -> str:
        """获取通用提取提示词"""
        return f"""
请从以下文本中提取所有有用的结构化信息，返回JSON格式：

{text}

请识别并提取所有可能的字段和值。
"""
    
    async def extract_product_from_html(self, html_content: str, 
                                      product_id: str = None) -> FinancialProduct:
        """从HTML中提取完整的产品信息"""
        # 基础信息提取
        basic_info = self.extract_basic_info_from_html(html_content)
        
        # AI增强提取
        ai_info = await self.extract_with_ai(html_content, "product")
        
        # 合并信息
        merged_info = {**basic_info, **ai_info}
        
        # 生成产品ID（如果没有提供）
        if not product_id:
            product_id = merged_info.get('product_id') or \
                        merged_info.get('product_code') or \
                        f"product_{hash(html_content[:100]) % 10000}"
        
        # 创建产品详情
        details = ProductDetails(
            description=merged_info.get('description'),
            features=merged_info.get('features'),
            investment_strategy=merged_info.get('investment_strategy'),
            risk_disclosure=merged_info.get('risk_disclosure'),
            fee_structure=merged_info.get('fee_structure'),
            redemption_rules=merged_info.get('redemption_rules')
        )
        
        # 创建产品对象
        product = FinancialProduct(
            product_id=product_id,
            name=merged_info.get('name', '未知产品'),
            product_code=merged_info.get('product_code'),
            product_type=merged_info.get('product_type'),
            risk_level=merged_info.get('risk_level'),
            expected_return=merged_info.get('expected_return'),
            investment_period=merged_info.get('investment_period'),
            min_amount=merged_info.get('min_amount'),
            status=merged_info.get('status'),
            sale_start_date=merged_info.get('sale_start_date'),
            sale_end_date=merged_info.get('sale_end_date'),
            currency=merged_info.get('currency', 'CNY'),
            issuer=merged_info.get('issuer'),
            details=details
        )
        
        return product
    
    async def extract_product_list_from_html(self, html_content: str) -> List[Dict[str, Any]]:
        """从HTML中提取产品列表"""
        # 基础提取
        basic_products = self._extract_basic_product_list(html_content)
        
        # AI增强提取
        ai_result = await self.extract_with_ai(html_content, "product_list")
        ai_products = ai_result.get('products', [])
        
        # 合并结果
        all_products = basic_products + ai_products
        
        # 去重（基于产品名称）
        seen_names = set()
        unique_products = []
        for product in all_products:
            name = product.get('name', '')
            if name and name not in seen_names:
                seen_names.add(name)
                unique_products.append(product)
        
        return unique_products
    
    def _extract_basic_product_list(self, html_content: str) -> List[Dict[str, Any]]:
        """基础产品列表提取"""
        soup = BeautifulSoup(html_content, 'html.parser')
        products = []
        
        # 尝试找到产品列表容器
        list_selectors = [
            '.product-list', '.list', '[class*="product"]',
            'ul', 'ol', '.items', '[class*="list"]'
        ]
        
        for selector in list_selectors:
            container = soup.select_one(selector)
            if container:
                items = container.find_all(['li', 'div', 'tr'])
                for item in items:
                    product_info = self._extract_product_from_element(item)
                    if product_info.get('name'):
                        products.append(product_info)
                
                if products:
                    break
        
        return products
    
    def _extract_product_from_element(self, element) -> Dict[str, Any]:
        """从单个元素中提取产品信息"""
        info = {}
        text = element.get_text()
        
        # 提取产品名称（通常是最长的文本或在特定标签中）
        name_tags = element.find_all(['h1', 'h2', 'h3', 'h4', 'strong', 'b'])
        if name_tags:
            info['name'] = name_tags[0].get_text().strip()
        
        # 提取收益率
        return_match = re.search(r'([\d.]+%)', text)
        if return_match:
            info['expected_return'] = return_match.group(1)
        
        # 提取风险等级
        risk_match = re.search(r'([PR]\d+|[低中高]+风险)', text)
        if risk_match:
            info['risk_level'] = risk_match.group(1)
        
        return info
