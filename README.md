# APAS Crawl - 银行理财产品爬虫

基于 crawl4ai 的银行理财产品爬虫项目，专门用于爬取招商银行理财产品信息。

## 功能特性

- 🚀 使用 crawl4ai 进行高效网页爬取
- 📊 爬取理财产品列表和详情信息
- 🔍 监听网络请求并保存响应数据
- 🤖 集成 DeepSeek 大模型进行数据提取和结构化
- 🛡️ 内置防反爬逻辑
- 📁 多格式数据保存（HTML、JSON）
- ⚡ 异步处理提升性能

## 项目结构

```
apas-crawl/
├── src/
│   ├── __init__.py
│   ├── crawler/
│   │   ├── __init__.py
│   │   ├── base_crawler.py      # 基础爬虫类
│   │   ├── cmb_crawler.py       # 招商银行爬虫
│   │   └── network_monitor.py   # 网络请求监听
│   ├── models/
│   │   ├── __init__.py
│   │   └── product.py           # 数据模型
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── anti_detection.py    # 防反爬工具
│   │   ├── data_extractor.py    # 数据提取工具
│   │   └── file_manager.py      # 文件管理工具
│   └── config/
│       ├── __init__.py
│       └── settings.py          # 配置文件
├── data/                        # 数据存储目录
├── logs/                        # 日志目录
├── tests/                       # 测试目录
├── .env                         # 环境变量
├── pyproject.toml
└── README.md
```

## 安装和使用

### 1. 环境准备

```bash
# 使用 uv 管理项目
pip install uv
uv venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装依赖
uv pip install -e .
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# DeepSeek API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 爬虫配置
MAX_PRODUCTS=200
CONCURRENT_LIMIT=5
REQUEST_DELAY=2
```

### 3. 运行爬虫

```bash
python -m src.main
```

## 主要功能

1. **理财产品列表爬取**: 从指定URL获取理财产品列表
2. **产品详情获取**: 点击进入每个产品的详情页面
3. **网络请求监听**: 监听并保存特定关键字的网络请求
4. **数据结构化**: 使用大模型提取和结构化数据
5. **多格式保存**: 保存HTML、清洗后的HTML和JSON格式数据

## 输出数据格式

```json
{
  "product_id": "产品唯一标识",
  "name": "产品名称",
  "type": "产品类型",
  "risk_level": "风险等级",
  "expected_return": "预期收益率",
  "investment_period": "投资期限",
  "min_amount": "起购金额",
  "status": "产品状态",
  "details": {
    "description": "产品描述",
    "features": "产品特色",
    "investment_strategy": "投资策略"
  },
  "network_requests": [
    {
      "url": "请求URL",
      "response": "响应数据"
    }
  ]
}
```

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd apas-crawl
```

### 2. 安装依赖

```bash
# 安装uv (如果还没有安装)
pip install uv

# 创建虚拟环境
uv venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate

# 安装项目依赖
uv pip install -e .
```

### 3. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置DeepSeek API密钥等参数
# DEEPSEEK_API_KEY=your_api_key_here
```

### 4. 运行爬虫

```bash
# 方式1: 使用主程序
python -m src.main

# 方式2: 使用运行脚本
python run.py

# 方式3: 直接运行
python src/main.py
```

### 5. 查看结果

```bash
# 分析爬取数据
python analyze_data.py

# 启动Web查看器
python web_viewer.py
# 然后在浏览器中访问 http://localhost:8000
```

## 使用示例

### 基本爬取

```python
import asyncio
from src.crawler.cmb_crawler import CMBCrawler

async def main():
    async with CMBCrawler() as crawler:
        # 爬取产品列表
        products = await crawler.crawl_product_list()
        print(f"找到 {len(products)} 个产品")

        # 爬取所有产品详情
        result = await crawler.crawl_all_products()
        print(f"成功爬取 {result.successful_products} 个产品")

asyncio.run(main())
```

### 自定义配置

```python
from src.config.settings import config

# 修改配置
config.MAX_PRODUCTS = 50  # 限制产品数量
config.CONCURRENT_LIMIT = 3  # 并发限制
config.REQUEST_DELAY = 3.0  # 请求延迟
```

## 项目特色

### 🚀 高性能异步爬取
- 基于crawl4ai的异步爬虫框架
- 支持并发控制和速率限制
- 智能重试机制

### 🛡️ 强大的防反爬能力
- 随机User-Agent和请求头
- 模拟人类行为（滚动、点击、延迟）
- 代理支持和IP轮换

### 🤖 AI驱动的数据提取
- 集成DeepSeek大模型进行智能数据提取
- 自动识别和结构化产品信息
- 支持多种数据格式输出

### 📊 全面的网络监听
- 实时监听目标网络请求
- 自动保存API响应数据
- 支持关键字过滤

### 📁 完善的数据管理
- 多格式数据保存（HTML、JSON）
- 自动文件清理和归档
- 数据完整性验证

## 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DEEPSEEK_API_KEY` | DeepSeek API密钥 | - |
| `MAX_PRODUCTS` | 最大产品数量 | 200 |
| `CONCURRENT_LIMIT` | 并发限制 | 5 |
| `REQUEST_DELAY` | 请求延迟(秒) | 2.0 |
| `HEADLESS` | 无头浏览器模式 | true |
| `RANDOM_USER_AGENT` | 随机User-Agent | true |

### 网络监听关键字

项目会自动监听包含以下关键字的网络请求：
- `alllist` - 产品列表接口
- `get-history-net-value` - 历史净值
- `get-history-performance` - 历史业绩
- `get-history-profit` - 历史收益
- `prd-info` - 产品信息

## 输出数据格式

### 产品数据结构

```json
{
  "product_id": "CMB_12345",
  "name": "招银理财产品A",
  "product_code": "ABC123",
  "expected_return": "4.2%",
  "risk_level": "PR2",
  "min_amount": "1万元",
  "status": "在售",
  "details": {
    "description": "产品描述",
    "features": "产品特色"
  },
  "network_requests": [
    {
      "url": "https://api.example.com/prd-info",
      "response_data": {...}
    }
  ]
}
```

## 故障排除

### 常见问题

1. **爬虫启动失败**
   ```bash
   # 检查Python版本 (需要3.9+)
   python --version

   # 检查依赖安装
   uv pip list
   ```

2. **无法获取产品列表**
   - 检查网络连接
   - 确认目标URL是否可访问
   - 查看日志文件了解详细错误

3. **DeepSeek API调用失败**
   - 检查API密钥是否正确
   - 确认API额度是否充足
   - 查看网络是否能访问API服务

4. **数据提取不完整**
   - 增加请求延迟时间
   - 检查页面结构是否发生变化
   - 查看HTML文件确认数据是否存在

### 日志查看

```bash
# 查看最新日志
tail -f logs/crawler_$(date +%Y%m%d).log

# 查看错误日志
grep ERROR logs/crawler_*.log
```

## 注意事项

- 请遵守网站的robots.txt和使用条款
- 合理设置请求频率，避免对服务器造成压力
- 定期更新防反爬策略
- 妥善保管API密钥等敏感信息
- 建议在测试环境中先验证爬虫功能
