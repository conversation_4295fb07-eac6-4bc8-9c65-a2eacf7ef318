"""
理财产品数据模型
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class NetworkRequest(BaseModel):
    """网络请求数据模型"""
    url: str = Field(..., description="请求URL")
    method: str = Field(default="GET", description="请求方法")
    headers: Optional[Dict[str, str]] = Field(default=None, description="请求头")
    response_data: Optional[Any] = Field(default=None, description="响应数据")
    status_code: Optional[int] = Field(default=None, description="状态码")
    timestamp: datetime = Field(default_factory=datetime.now, description="请求时间")


class ProductDetails(BaseModel):
    """产品详情数据模型"""
    description: Optional[str] = Field(default=None, description="产品描述")
    features: Optional[str] = Field(default=None, description="产品特色")
    investment_strategy: Optional[str] = Field(default=None, description="投资策略")
    risk_disclosure: Optional[str] = Field(default=None, description="风险揭示")
    fee_structure: Optional[str] = Field(default=None, description="费用结构")
    redemption_rules: Optional[str] = Field(default=None, description="赎回规则")
    performance_benchmark: Optional[str] = Field(default=None, description="业绩比较基准")
    investment_scope: Optional[str] = Field(default=None, description="投资范围")
    manager_info: Optional[str] = Field(default=None, description="管理人信息")
    custodian_info: Optional[str] = Field(default=None, description="托管人信息")


class FinancialProduct(BaseModel):
    """理财产品数据模型"""
    product_id: str = Field(..., description="产品唯一标识")
    name: str = Field(..., description="产品名称")
    product_code: Optional[str] = Field(default=None, description="产品代码")
    product_type: Optional[str] = Field(default=None, description="产品类型")
    risk_level: Optional[str] = Field(default=None, description="风险等级")
    expected_return: Optional[str] = Field(default=None, description="预期收益率")
    actual_return: Optional[str] = Field(default=None, description="实际收益率")
    investment_period: Optional[str] = Field(default=None, description="投资期限")
    min_amount: Optional[str] = Field(default=None, description="起购金额")
    increment_amount: Optional[str] = Field(default=None, description="递增金额")
    status: Optional[str] = Field(default=None, description="产品状态")
    sale_start_date: Optional[str] = Field(default=None, description="销售起始日")
    sale_end_date: Optional[str] = Field(default=None, description="销售结束日")
    value_date: Optional[str] = Field(default=None, description="起息日")
    maturity_date: Optional[str] = Field(default=None, description="到期日")
    currency: Optional[str] = Field(default="CNY", description="币种")
    issuer: Optional[str] = Field(default=None, description="发行机构")
    
    # 详细信息
    details: Optional[ProductDetails] = Field(default=None, description="产品详情")
    
    # 网络请求记录
    network_requests: List[NetworkRequest] = Field(default_factory=list, description="相关网络请求")
    
    # 元数据
    crawl_time: datetime = Field(default_factory=datetime.now, description="爬取时间")
    source_url: Optional[str] = Field(default=None, description="来源URL")
    detail_url: Optional[str] = Field(default=None, description="详情页URL")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CrawlResult(BaseModel):
    """爬取结果数据模型"""
    total_products: int = Field(..., description="总产品数量")
    successful_products: int = Field(..., description="成功爬取的产品数量")
    failed_products: int = Field(..., description="失败的产品数量")
    products: List[FinancialProduct] = Field(default_factory=list, description="产品列表")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    crawl_start_time: datetime = Field(default_factory=datetime.now, description="爬取开始时间")
    crawl_end_time: Optional[datetime] = Field(default=None, description="爬取结束时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
