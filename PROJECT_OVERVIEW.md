# 招商银行理财产品爬虫项目概览

## 项目简介

这是一个基于 crawl4ai 的银行理财产品爬虫项目，专门用于爬取招商银行理财产品信息。项目采用现代化的异步爬虫架构，集成了AI数据提取、网络监听、防反爬等功能。

## 核心特性

### 🚀 技术栈
- **爬虫框架**: crawl4ai (基于Playwright)
- **异步处理**: asyncio + aiohttp
- **AI集成**: DeepSeek大模型
- **数据模型**: Pydantic
- **日志系统**: loguru
- **包管理**: uv

### 🛡️ 防反爬能力
- 随机User-Agent和请求头
- 智能延迟和速率限制
- 模拟人类行为（滚动、点击）
- 代理支持
- 请求重试机制

### 📊 数据处理
- 多格式数据保存（HTML、JSON）
- AI驱动的数据提取和结构化
- 网络请求监听和记录
- 数据完整性验证

## 项目结构

```
apas-crawl/
├── src/                          # 源代码目录
│   ├── config/                   # 配置模块
│   │   ├── __init__.py
│   │   └── settings.py           # 项目配置
│   ├── crawler/                  # 爬虫模块
│   │   ├── __init__.py
│   │   ├── base_crawler.py       # 基础爬虫类
│   │   ├── cmb_crawler.py        # 招商银行爬虫
│   │   └── network_monitor.py    # 网络监听器
│   ├── models/                   # 数据模型
│   │   ├── __init__.py
│   │   └── product.py            # 产品数据模型
│   ├── utils/                    # 工具模块
│   │   ├── __init__.py
│   │   ├── anti_detection.py     # 防反爬工具
│   │   ├── data_extractor.py     # 数据提取器
│   │   └── file_manager.py       # 文件管理器
│   └── main.py                   # 主程序入口
├── tests/                        # 测试目录
│   ├── __init__.py
│   └── test_crawler.py           # 爬虫测试
├── data/                         # 数据存储目录
│   ├── html/                     # HTML文件
│   ├── json/                     # JSON数据
│   └── network/                  # 网络请求数据
├── logs/                         # 日志目录
├── pyproject.toml                # 项目配置
├── README.md                     # 项目说明
├── .env.example                  # 环境变量模板
├── run.py                        # 运行脚本
├── install.py                    # 安装脚本
├── analyze_data.py               # 数据分析脚本
├── web_viewer.py                 # Web查看器
└── Makefile                      # 项目管理
```

## 核心模块说明

### 1. 配置模块 (src/config/)
- `settings.py`: 统一的项目配置管理，支持环境变量

### 2. 爬虫模块 (src/crawler/)
- `base_crawler.py`: 抽象基类，定义爬虫通用功能
- `cmb_crawler.py`: 招商银行专用爬虫实现
- `network_monitor.py`: 网络请求监听和记录

### 3. 数据模型 (src/models/)
- `product.py`: 理财产品数据结构定义

### 4. 工具模块 (src/utils/)
- `anti_detection.py`: 防反爬检测工具
- `data_extractor.py`: AI驱动的数据提取
- `file_manager.py`: 文件操作和管理

## 工作流程

### 1. 初始化阶段
```python
# 创建爬虫实例
crawler = CMBCrawler()
await crawler.start()
```

### 2. 产品列表爬取
```python
# 访问列表页面
products = await crawler.crawl_product_list()
# 解析产品基础信息
# 保存HTML和JSON数据
```

### 3. 产品详情爬取
```python
for product_info in products:
    # 访问详情页面
    product = await crawler.crawl_product_detail(product_info)
    # AI提取结构化数据
    # 监听网络请求
    # 保存完整数据
```

### 4. 数据处理和保存
```python
# 多格式保存
await file_manager.save_html(html, filename)
await file_manager.save_json(data, filename)
await file_manager.save_product(product)
```

## 数据流转

```mermaid
graph TD
    A[目标URL] --> B[列表页面爬取]
    B --> C[HTML解析]
    C --> D[产品基础信息提取]
    D --> E[产品详情页面爬取]
    E --> F[网络请求监听]
    F --> G[AI数据提取]
    G --> H[数据结构化]
    H --> I[多格式保存]
    I --> J[数据分析和展示]
```

## 关键技术实现

### 1. 异步爬虫架构
```python
async with CMBCrawler() as crawler:
    result = await crawler.crawl_all_products()
```

### 2. 网络请求监听
```python
# 监听特定关键字的网络请求
keywords = ["alllist", "prd-info", "get-history-net-value"]
captured_requests = network_monitor.get_captured_requests()
```

### 3. AI数据提取
```python
# 使用DeepSeek提取结构化数据
extracted_data = await data_extractor.extract_with_ai(html_content)
```

### 4. 防反爬策略
```python
# 随机延迟和请求头
await anti_detection.random_delay()
headers = anti_detection.get_random_headers()
```

## 配置说明

### 环境变量
- `DEEPSEEK_API_KEY`: DeepSeek API密钥
- `MAX_PRODUCTS`: 最大产品数量限制
- `CONCURRENT_LIMIT`: 并发请求限制
- `REQUEST_DELAY`: 请求间隔延迟

### 监听关键字
项目会自动监听包含以下关键字的网络请求：
- `alllist`: 产品列表接口
- `get-history-net-value`: 历史净值数据
- `get-history-performance`: 历史业绩数据
- `prd-info`: 产品详细信息

## 输出数据格式

### 产品数据结构
```json
{
  "product_id": "CMB_12345",
  "name": "招银理财产品A",
  "expected_return": "4.2%",
  "risk_level": "PR2",
  "min_amount": "1万元",
  "details": {
    "description": "产品描述",
    "features": "产品特色"
  },
  "network_requests": [...]
}
```

## 扩展性设计

### 1. 多银行支持
- 继承`BaseCrawler`类
- 实现特定银行的爬取逻辑
- 复用通用功能模块

### 2. 数据源扩展
- 添加新的数据提取器
- 支持更多数据格式
- 集成其他AI模型

### 3. 存储后端扩展
- 支持数据库存储
- 云存储集成
- 实时数据流处理

## 性能优化

### 1. 并发控制
- 使用信号量限制并发数
- 智能重试机制
- 资源池管理

### 2. 缓存策略
- HTML内容缓存
- 网络请求去重
- 数据增量更新

### 3. 内存管理
- 流式数据处理
- 及时释放资源
- 垃圾回收优化

## 监控和调试

### 1. 日志系统
- 分级日志记录
- 文件轮转和压缩
- 实时日志查看

### 2. 数据分析
- 爬取统计报告
- 成功率分析
- 错误模式识别

### 3. Web界面
- 实时数据查看
- 爬取进度监控
- 交互式数据分析

## 部署建议

### 1. 开发环境
```bash
make quickstart  # 快速开始
make run         # 运行爬虫
make test        # 运行测试
```

### 2. 生产环境
- 使用Docker容器化部署
- 配置定时任务
- 监控和告警设置
- 数据备份策略

### 3. 云部署
- 支持AWS、阿里云等云平台
- 弹性伸缩配置
- 分布式爬取架构
