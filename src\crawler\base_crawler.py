"""
基础爬虫类
"""
import asyncio
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from loguru import logger
from src.config.settings import config
from src.utils.anti_detection import AntiDetection
from src.utils.file_manager import FileManager
from src.utils.data_extractor import DataExtractor
from src.crawler.network_monitor import NetworkMonitor
from src.models.product import FinancialProduct, CrawlResult


class BaseCrawler(ABC):
    """基础爬虫类"""
    
    def __init__(self):
        self.anti_detection = AntiDetection()
        self.file_manager = FileManager()
        self.data_extractor = DataExtractor()
        self.network_monitor = NetworkMonitor()
        self.crawler = None
        self.semaphore = asyncio.Semaphore(config.CONCURRENT_LIMIT)
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动爬虫"""
        try:
            browser_config = self.anti_detection.get_browser_options()
            
            self.crawler = AsyncWebCrawler(
                headless=browser_config["headless"],
                browser_type=config.BROWSER_TYPE,
                verbose=True
            )
            
            await self.crawler.astart()
            logger.info("爬虫已启动")
            
        except Exception as e:
            logger.error(f"启动爬虫失败: {e}")
            raise
    
    async def close(self):
        """关闭爬虫"""
        try:
            if self.crawler:
                await self.crawler.aclose()
                logger.info("爬虫已关闭")
        except Exception as e:
            logger.error(f"关闭爬虫失败: {e}")
    
    async def crawl_page(self, url: str, wait_for: Optional[str] = None,
                        js_code: Optional[str] = None) -> Dict[str, Any]:
        """爬取单个页面"""
        async with self.semaphore:
            try:
                # 防反爬延迟
                await self.anti_detection.rate_limit()
                
                # 获取随机请求头
                headers = self.anti_detection.get_random_headers()
                
                logger.info(f"开始爬取页面: {url}")
                
                # 使用crawl4ai爬取
                result = await self.crawler.arun(
                    url=url,
                    headers=headers,
                    wait_for=wait_for,
                    js_code=js_code,
                    page_timeout=config.TIMEOUT * 1000,  # 转换为毫秒
                    delay_before_return_html=2.0
                )
                
                if result.success:
                    logger.info(f"页面爬取成功: {url}")
                    return {
                        "success": True,
                        "html": result.html,
                        "cleaned_html": result.cleaned_html,
                        "markdown": result.markdown,
                        "links": result.links,
                        "media": result.media,
                        "metadata": result.metadata
                    }
                else:
                    logger.error(f"页面爬取失败: {url}, 错误: {result.error_message}")
                    return {
                        "success": False,
                        "error": result.error_message
                    }
                    
            except Exception as e:
                logger.error(f"爬取页面异常 {url}: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
    
    async def crawl_with_browser(self, url: str, 
                               page_actions: Optional[callable] = None) -> Dict[str, Any]:
        """使用浏览器爬取（支持JavaScript和交互）"""
        async with self.semaphore:
            try:
                await self.anti_detection.rate_limit()
                
                # 获取浏览器配置
                browser_config = self.anti_detection.get_browser_options()
                headers = self.anti_detection.get_random_headers()
                
                logger.info(f"使用浏览器爬取页面: {url}")
                
                # 使用crawl4ai的浏览器模式
                js_code = """
                // 等待页面加载完成
                await new Promise(resolve => {
                    if (document.readyState === 'complete') {
                        resolve();
                    } else {
                        window.addEventListener('load', resolve);
                    }
                });
                
                // 滚动页面以触发懒加载
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 1000));
                """
                
                if page_actions:
                    # 如果有自定义页面操作，添加到JS代码中
                    custom_js = await page_actions()
                    if custom_js:
                        js_code += f"\n{custom_js}"
                
                result = await self.crawler.arun(
                    url=url,
                    headers=headers,
                    js_code=js_code,
                    wait_for="body",
                    page_timeout=config.TIMEOUT * 1000,
                    delay_before_return_html=3.0,
                    simulate_user=True,
                    override_navigator=True
                )
                
                if result.success:
                    logger.info(f"浏览器爬取成功: {url}")
                    return {
                        "success": True,
                        "html": result.html,
                        "cleaned_html": result.cleaned_html,
                        "markdown": result.markdown,
                        "links": result.links,
                        "media": result.media,
                        "metadata": result.metadata
                    }
                else:
                    logger.error(f"浏览器爬取失败: {url}, 错误: {result.error_message}")
                    return {
                        "success": False,
                        "error": result.error_message
                    }
                    
            except Exception as e:
                logger.error(f"浏览器爬取异常 {url}: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
    
    async def retry_crawl(self, url: str, max_retries: int = None) -> Dict[str, Any]:
        """重试爬取"""
        if max_retries is None:
            max_retries = config.RETRY_TIMES
        
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # 重试前等待更长时间
                    wait_time = min(2 ** attempt, 10)  # 指数退避，最大10秒
                    logger.info(f"第{attempt}次重试，等待{wait_time}秒...")
                    await asyncio.sleep(wait_time)
                
                result = await self.crawl_page(url)
                
                if result["success"]:
                    if attempt > 0:
                        logger.info(f"重试成功: {url} (第{attempt}次重试)")
                    return result
                else:
                    last_error = result.get("error", "未知错误")
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"第{attempt}次尝试失败 {url}: {e}")
        
        logger.error(f"重试{max_retries}次后仍然失败: {url}, 最后错误: {last_error}")
        return {
            "success": False,
            "error": f"重试{max_retries}次后失败: {last_error}"
        }
    
    @abstractmethod
    async def crawl_product_list(self) -> List[Dict[str, Any]]:
        """爬取产品列表 - 子类必须实现"""
        pass
    
    @abstractmethod
    async def crawl_product_detail(self, product_info: Dict[str, Any]) -> FinancialProduct:
        """爬取产品详情 - 子类必须实现"""
        pass
    
    async def crawl_all_products(self) -> CrawlResult:
        """爬取所有产品"""
        result = CrawlResult(
            total_products=0,
            successful_products=0,
            failed_products=0
        )
        
        try:
            # 1. 爬取产品列表
            logger.info("开始爬取产品列表...")
            product_list = await self.crawl_product_list()
            
            if not product_list:
                logger.warning("未获取到产品列表")
                return result
            
            # 限制产品数量
            if len(product_list) > config.MAX_PRODUCTS:
                product_list = product_list[:config.MAX_PRODUCTS]
                logger.info(f"产品数量限制为{config.MAX_PRODUCTS}个")
            
            result.total_products = len(product_list)
            logger.info(f"获取到{len(product_list)}个产品")
            
            # 2. 并发爬取产品详情
            logger.info("开始爬取产品详情...")
            tasks = []
            
            for product_info in product_list:
                task = self._crawl_single_product(product_info, result)
                tasks.append(task)
            
            # 执行所有任务
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 3. 设置结束时间
            result.crawl_end_time = asyncio.get_event_loop().time()
            
            logger.info(f"爬取完成: 总计{result.total_products}个产品, "
                       f"成功{result.successful_products}个, "
                       f"失败{result.failed_products}个")
            
            return result
            
        except Exception as e:
            logger.error(f"爬取过程异常: {e}")
            result.errors.append(str(e))
            return result
    
    async def _crawl_single_product(self, product_info: Dict[str, Any], 
                                  result: CrawlResult):
        """爬取单个产品"""
        try:
            product = await self.crawl_product_detail(product_info)
            result.products.append(product)
            result.successful_products += 1
            
            # 保存产品数据
            await self.file_manager.save_product(product)
            
        except Exception as e:
            logger.error(f"爬取产品详情失败 {product_info}: {e}")
            result.failed_products += 1
            result.errors.append(f"产品{product_info}爬取失败: {str(e)}")
