#!/usr/bin/env python3
"""
测试HTML解析功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.crawler.cmb_crawler import CMBCrawler


async def test_html_parsing():
    """测试HTML解析功能"""
    print("🧪 测试招商银行产品列表HTML解析")
    print("=" * 50)
    
    # 读取示例HTML文件
    html_file = Path("example/product_list.html")
    if not html_file.exists():
        print("❌ 示例HTML文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 HTML文件大小: {len(html_content)} 字符")
    
    # 创建爬虫实例
    crawler = CMBCrawler()
    
    try:
        # 解析产品列表
        print("\n🔍 开始解析产品列表...")
        products = await crawler._parse_product_list_detailed(html_content)
        
        print(f"\n📊 解析结果:")
        print(f"总产品数量: {len(products)}")
        
        if products:
            print(f"\n📋 产品详情:")
            for i, product in enumerate(products[:5], 1):  # 只显示前5个产品
                print(f"\n--- 产品 {i} ---")
                print(f"产品ID: {product.get('product_id', 'N/A')}")
                print(f"产品名称: {product.get('name', 'N/A')}")
                print(f"管理机构: {product.get('issuer', 'N/A')}")
                print(f"预期收益: {product.get('expected_return', 'N/A')}")
                print(f"收益类型: {product.get('return_type', 'N/A')}")
                print(f"风险等级: {product.get('risk_level', 'N/A')}")
                print(f"产品标签: {product.get('tags', [])}")
                print(f"交易信息: {product.get('trade_info', [])}")
                print(f"到账时间: {product.get('arrival_time', 'N/A')}")
                print(f"行为ID: {product.get('behavior_id', 'N/A')}")
            
            if len(products) > 5:
                print(f"\n... 还有 {len(products) - 5} 个产品")
            
            # 统计信息
            print(f"\n📈 统计信息:")
            
            # 管理机构统计
            issuers = [p.get('issuer') for p in products if p.get('issuer')]
            if issuers:
                from collections import Counter
                issuer_count = Counter(issuers)
                print(f"管理机构分布:")
                for issuer, count in issuer_count.most_common():
                    print(f"  {issuer}: {count} 个产品")
            
            # 收益率统计
            returns = []
            for p in products:
                return_str = p.get('expected_return', '')
                if return_str and '%' in return_str:
                    try:
                        return_val = float(return_str.replace('%', ''))
                        returns.append(return_val)
                    except ValueError:
                        pass
            
            if returns:
                print(f"收益率统计:")
                print(f"  平均收益率: {sum(returns)/len(returns):.2f}%")
                print(f"  最高收益率: {max(returns):.2f}%")
                print(f"  最低收益率: {min(returns):.2f}%")
                print(f"  有收益率数据的产品: {len(returns)} 个")
            
            # 风险等级统计
            risk_levels = [p.get('risk_level') for p in products if p.get('risk_level')]
            if risk_levels:
                risk_count = Counter(risk_levels)
                print(f"风险等级分布:")
                for risk, count in risk_count.most_common():
                    print(f"  {risk}: {count} 个产品")
            
            print(f"\n✅ HTML解析测试成功!")
            return True
        else:
            print("❌ 未解析到任何产品")
            return False
            
    except Exception as e:
        print(f"❌ 解析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_data_extractor():
    """测试数据提取器"""
    print("\n🔧 测试数据提取器功能")
    print("=" * 50)
    
    from src.utils.data_extractor import DataExtractor
    
    # 读取示例HTML文件
    html_file = Path("example/product_list.html")
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    extractor = DataExtractor()
    
    try:
        # 测试基础信息提取
        print("🔍 测试基础信息提取...")
        basic_info = extractor.extract_basic_info_from_html(html_content)
        print(f"基础信息提取结果: {basic_info}")
        
        # 测试产品列表提取
        print("\n🔍 测试产品列表提取...")
        products = await extractor.extract_product_list_from_html(html_content)
        print(f"数据提取器找到 {len(products)} 个产品")
        
        if products:
            print("前3个产品:")
            for i, product in enumerate(products[:3], 1):
                print(f"  {i}. {product}")
        
        print("✅ 数据提取器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据提取器测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 招商银行HTML解析测试")
    print("=" * 60)
    
    try:
        # 运行异步测试
        success1 = asyncio.run(test_html_parsing())
        success2 = asyncio.run(test_data_extractor())
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 所有测试通过!")
            print("\n📋 下一步:")
            print("1. 配置.env文件中的API密钥")
            print("2. 运行完整爬虫测试: python run.py")
        else:
            print("❌ 部分测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
