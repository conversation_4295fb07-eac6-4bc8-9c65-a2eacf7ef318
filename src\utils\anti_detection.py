"""
防反爬工具
"""
import random
import time
import asyncio
from typing import Dict, List, Optional
from fake_useragent import UserAgent
from src.config.settings import config


class AntiDetection:
    """防反爬检测工具类"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.request_count = 0
        self.last_request_time = 0
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        if config.RANDOM_USER_AGENT:
            try:
                return self.ua.random
            except Exception:
                # 如果获取失败，使用预设的移动端User-Agent
                return random.choice(config.USER_AGENTS)
        return config.USER_AGENTS[0]
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = {
            "User-Agent": self.get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "max-age=0"
        }
        
        # 随机添加一些可选头部
        optional_headers = {
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://mobile.cmbchina.com/",
            "Origin": "https://mobile.cmbchina.com"
        }
        
        if random.random() > 0.5:
            headers.update(random.sample(list(optional_headers.items()), 
                                       random.randint(1, len(optional_headers))))
        
        return headers
    
    async def random_delay(self, min_delay: float = None, max_delay: float = None):
        """随机延迟"""
        if not config.RANDOM_DELAY:
            await asyncio.sleep(config.REQUEST_DELAY)
            return
        
        if min_delay is None:
            min_delay = config.REQUEST_DELAY * 0.5
        if max_delay is None:
            max_delay = config.REQUEST_DELAY * 1.5
        
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    def should_delay(self) -> bool:
        """判断是否需要延迟"""
        current_time = time.time()
        if current_time - self.last_request_time < config.REQUEST_DELAY:
            return True
        return False
    
    async def rate_limit(self):
        """速率限制"""
        current_time = time.time()
        time_diff = current_time - self.last_request_time
        
        if time_diff < config.REQUEST_DELAY:
            await asyncio.sleep(config.REQUEST_DELAY - time_diff)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def get_browser_options(self) -> Dict:
        """获取浏览器选项"""
        options = {
            "headless": config.HEADLESS,
            "args": [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",  # 禁用图片加载以提高速度
                "--disable-javascript",  # 可选：禁用JavaScript
                "--user-agent=" + self.get_random_user_agent()
            ]
        }
        
        if config.USE_PROXY and config.PROXY_URL:
            options["args"].append(f"--proxy-server={config.PROXY_URL}")
        
        return options
    
    def simulate_human_behavior(self) -> Dict:
        """模拟人类行为参数"""
        return {
            "viewport": {
                "width": random.randint(360, 414),
                "height": random.randint(640, 896)
            },
            "scroll_pause_time": random.uniform(0.5, 2.0),
            "click_delay": random.uniform(0.1, 0.5),
            "typing_delay": random.uniform(0.05, 0.2)
        }
    
    async def simulate_scroll(self, page, scroll_count: int = 3):
        """模拟滚动行为"""
        for _ in range(scroll_count):
            await page.evaluate("window.scrollBy(0, window.innerHeight * 0.8)")
            await asyncio.sleep(random.uniform(0.5, 1.5))
    
    async def simulate_mouse_movement(self, page):
        """模拟鼠标移动"""
        try:
            # 随机移动鼠标
            x = random.randint(100, 300)
            y = random.randint(100, 400)
            await page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.1, 0.3))
        except Exception:
            pass  # 忽略鼠标移动错误
